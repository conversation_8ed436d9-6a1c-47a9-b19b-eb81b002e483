#!/usr/bin/env bash
#


# Check if podman is available
if command -v podman &> /dev/null; then
    container_runtime="podman"
# If podman is not available, check if docker is available
elif command -v docker &> /dev/null; then
    container_runtime="docker"
else
    echo "Error: Neither docker nor podman is installed. Exiting."
    exit 1
fi

echo "Stopping serve process..."
${container_runtime}  kill showroom-httpd
echo "Stopped serve process."