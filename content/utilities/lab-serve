#!/usr/bin/env bash
#

# Check if podman is available
if command -v podman &> /dev/null; then
    container_runtime="podman"
# If podman is not available, check if docker is available
elif command -v docker &> /dev/null; then
    container_runtime="docker"
else
    echo "Error: Neither docker nor podman is installed. Exiting."
    exit 1
fi


echo "Starting serve process..."
# TODO: Add case statement to allow stopping, starting, and restarting
# TODO: Add logic to detect both podman and docker, if both are installed, use podman as default "first found"
# TODO: Move to RHEL image
${container_runtime} run -it --rm \
    --name showroom-httpd \
    -p 8443:80 \
    -v "${PWD}/content/www:/usr/local/apache2/htdocs/" \
    docker.io/httpd:2.4

echo "Serving lab content on http://localhost:8443/index.html"