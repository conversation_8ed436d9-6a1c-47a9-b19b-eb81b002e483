#!/usr/bin/env bash
#

# On Mac run with
# fswatch -e ./content/www/ -o ./  | xargs -n1 -I{} ./content/utilities/lab-reload

# Check if podman is available
if command -v podman &> /dev/null; then
    container_runtime="podman"
# If podman is not available, check if docker is available
elif command -v docker &> /dev/null; then
    container_runtime="docker"
else
    echo "Error: Neither docker nor podman is installed. Exiting."
    exit 1
fi


# If the color table file exists,
if [[ -f "${coltable}" ]]; then
    # source it
    source "${coltable}"
# Otherwise,
else
    # Set these values so the installer can still run in color
    COL_NC='\e[0m' # No Color
    COL_LIGHT_GREEN='\e[1;32m'
    COL_LIGHT_RED='\e[1;31m'
    TICK="[${COL_LIGHT_GREEN}✓${COL_NC}]"
    CROSS="[${COL_LIGHT_RED}✗${COL_NC}]"
    INFO="[i]"
    # shellcheck disable=SC2034
    DONE="${COL_LIGHT_GREEN} done!${COL_NC}"
    OVER="\\r\\033[K"
fi

# printf "  %b Stopping Serving container %s\\n" "${INFO}"
# ${container_runtime} container stop showroom-httpd
# printf "%b  %b %s\\n" "${OVER}" "${TICK}" "Container stopped "

printf "  %b Cleaning site %s\\n" "${INFO}"
rm -rf ./content/www/*
printf "%b  %b %s\\n" "${OVER}" "${TICK}" "Site Cleaned "

printf "  %b Rebuilding site %s\\n" "${INFO}"
mkdir -p ./content/www

time \
${container_runtime} run --rm --name showroom-builder \
    --platform linux/amd64 \
    -v "${PWD}:/antora" \
    docker.io/antora/antora default-site.yml
printf "%b  %b %s\\n" "${OVER}" "${TICK}" "Site Rebuilt "

# printf "  %b Caching site %s\\n" "${INFO}"
# rm -rf ./content/www-cache/
# cp -rp  ./content/www/ \
#         ./content/www-cache/

printf "  %b (Re-)Starting serving container %s\\n" "${INFO}"

CONTAINER_NAME="showroom-httpd"

if ${container_runtime} inspect --format '{{.State.Running}}' $CONTAINER_NAME &>/dev/null; then
    ${container_runtime} restart $CONTAINER_NAME
    printf "%b  %b %s\\n" "${OVER}" "${TICK}" "container restarted "
else
    ${container_runtime} run --rm \
        -d \
        --name showroom-httpd \
        -p 8443:80 \
        -v "${PWD}/content/www:/usr/local/apache2/htdocs/" \
        docker.io/httpd:2.4
    printf "%b  %b %s\\n" "${OVER}" "${TICK}" "container started "
fi

printf "%b  %b %s\\n" "${OVER}" "${TICK}" "Site is served! "
printf "%b  %b %s\\n" "${OVER}" "${TICK}" "Click on this link: http://localhost:8443/index.html "
printf "%-100s\n" | tr ' ' '-'

