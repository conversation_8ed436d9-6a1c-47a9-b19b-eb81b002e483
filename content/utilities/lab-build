#!/usr/bin/env bash
#

# Check if podman is available
if command -v podman &> /dev/null; then
    container_runtime="podman"
# If podman is not available, check if docker is available
elif command -v docker &> /dev/null; then
    container_runtime="docker"
else
    echo "Error: Neither docker nor podman is installed. Exiting."
    exit 1
fi

echo "Starting build process..."
echo "Removing old site..."
rm -rf ./content/www/*

echo "Building new site..."
mkdir -p ./content/www

${container_runtime} run --rm --name showroom-builder \
    --platform linux/amd64 \
    -v "${PWD}:/antora" \
    docker.io/antora/antora default-site.yml

echo "Build process complete. Check the ./content/www folder for the generated site."
echo "To view the site locally, run the following command: utilities/lab-serve"
echo "If already running then browse to http://localhost:8443/index.html"

# ${container_runtime} run \
#     --rm\
#     -it\
#     --name showroom-builder \
#     --platform linux/amd64 \
#     -v "${PWD}:/antora" \
#     --entrypoint=sh \
#     docker.io/antora/antora


