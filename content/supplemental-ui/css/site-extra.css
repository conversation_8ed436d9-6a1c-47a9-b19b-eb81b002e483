/* left align items inside imageblock */
.doc .imageblock {
    align-items: flex-start;
  }

/* add a shadow to the image container */
.doc .bordershadow .content{
    border: solid 1px black;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

/*custom video css*/
video {
    border: solid 1px black;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

/* allow for wider screens */
.doc {
    max-width: none;
}

.navbar-item-right-dropdown {
    float: right!important;
}

.navbar-dropdown-right {
    right: 0;
    left: auto;
}

.navbar-item-right-content {
    width: 100%;
    background-color: #131313;
}