= Deploying the application via GitOps
include::_attributes.adoc[]

== Deploy the application(s) via GitOps

We are going to use OpenShift GitOps/{argocd} to deploy the application in your namespace.

- Access the {argocd} Console by clicking on the link in the top right menu.
+
[.bordershadow]
image::05/05-open-argocd.png[]

- Click on "Log in via OpenShift" to log in with your credentials.
+
[.bordershadow]
image::05/05-login-argocd-openshift.png[]
+
[.bordershadow]
image::05/05-openshift-login.png[]

- Once logged in, you will be redirected to the {argocd} Console. In the "Applications" tab, click on "Create Application".
+
[.bordershadow]
image::05/05-create-application.png[]

- On the top right corner of the panel that opened, click on "Edit as YAML".
+
[.bordershadow]
image::05/05-edit-as-yaml.png[]

- Copy the content of the following text, paste it in the editor (replace the default content), then click on "Save".
+
[source, yaml, subs="attributes+"]
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: claim-insurance-app-{user}
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: {user}
  project: project-{user}
  source:
    path: lab-materials/05/app
    repoURL: https://github.com/rh-aiservices-bu/parasol-insurance.git
    targetRevision: dev
  syncPolicy:
    automated:
      prune: true
      selfHeal: false
    syncOptions: [CreateNamespace=false]
+
[.bordershadow]
image::05/05-application-yaml-save.png[]

- Back on the form view, click on "Create".
+
[.bordershadow]
image::05/05-create-application-validation.png[]

- The Application will start deploying. You can click on the Card to see the details.
+
[.bordershadow]
image::05/05-application-creation.png[]

- After a few seconds, all the elements will be deployed, and the Application will report as "Healthy" and "Synced".
+
[.bordershadow]
image::05/05-application-deployed.png[] 

- You can now access the application at the following URL:
https://ic-app-{user}.{openshift_cluster_ingress_domain}/[https://ic-app-{user}.{openshift_cluster_ingress_domain}/,window=_blank]

Open the the URL in your browser to access the application and head to the next step.
