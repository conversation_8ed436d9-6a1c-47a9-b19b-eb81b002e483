= Validating the environment
include::_attributes.adoc[]

Now that you are connected to your workbench, let's make sure that all the expected services are responding properly in the cluster.

In your workbench:

. In the left hand navigation menu, navigate to the folder called: `parasol-insurance/lab-materials/02`

. Open the notebook called `02-05-validating.ipynb`

. If you have never executed Cells in a Jupyter Notebook before, here is what you need to do:

.. Click on the **Restart kernel and Run all Cells** link:
+
[.bordershadow]
image::02/02-05-restart-and-run.png[]
.. Click **Restart** :
+
[.bordershadow]
image::02/02-05-restart-kernel.png[]
+
. Running these cells will confirm that all the lab-required services are responding.

The output should look as follows:

[source,console]
----
Success: Minio is reachable on minio.ic-shared-minio.svc.cluster.local:9000
Success: Gitea is reachable on gitea.gitea.svc.cluster.local:3000
Success: Postgres Database is reachable on claimdb.ic-shared-db.svc.cluster.local:5432
Success: LLM Service is reachable on llm.ic-shared-llm.svc.cluster.local:8000
Success: LLM Service-Qwen2.5 is reachable on qwen-predictor.ic-shared-llm.svc.cluster.local:8080
Success: ModelMesh is reachable on modelmesh-serving.ic-shared-img-det.svc.cluster.local:8033
Success: Milvus Vector DB is reachable on vectordb-milvus.ic-shared-milvus.svc.cluster.local:19530
----

If the output of this notebook looks suspicious, please inform the people leading the {ic}.

== Overall view

This is a summarized visualization of how the environment is laid out:

[.bordershadow]
image::02/ic-eng-diag.drawio.svg[]
