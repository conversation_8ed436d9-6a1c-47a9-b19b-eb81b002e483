= Creating a workbench
include::_attributes.adoc[]

IMPORTANT: If you've used the pre-created workbench, you do not need to perform this section. Only do so if you are interested in how a workbench is created.

== Launch a Workbench

* Once the Data Connection and Pipeline Server are fully created, create a Workbench:
+
[.bordershadow]
image::02/02-03-create-wb.png[]
* Make sure it has the following characteristics:
** Choose a name for it, like: `My Workbench`
** Image Selection: `CUSTOM - {ic} Workbench`
** Container Size: `Standard`
** Accelerator: `None`
* That should look like:
+
[.bordershadow]
image::02/02-02-launch-workbench-01.png[]
NOTE: You should **not** need to modify any other Workbench settings (such as Storage)
* Wait for your workbench to be fully started
* Once it is, click the **Open** Link to connect to it:
+
[.bordershadow]
image::02/02-03-open-link.png[]

* Authenticate with the same credentials as earlier
* You will be asked to accept the following settings:
+
[.bordershadow]
image::02/02-02-accept.png[]

* Click on *Allow selected permissions*
* You should now see this:
+
[.bordershadow]
image::02/02-02-jupyter.png[]

== Git-Clone the common repo

We will clone the content of our Git repo so that you can access all the materials that were created as part of our prototyping exercise.

* Using the Git UI:
** Open the Git UI in Jupyter:
+
[.bordershadow]
image::02/git-clone-1.png[]
+
** Enter the URL of the Git repo:
+
[.console-input]
[source,adoc]
[subs=attributes+]
----
{git-clone-repo-url}
----
+
[.bordershadow]
image::02/git-clone-2.png[]

At this point, your project is ready for the work we want to do in it.
