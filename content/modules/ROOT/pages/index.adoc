= {lab_name}
include::_attributes.adoc[]

== {ic} Overview

This {ic-lab} will illustrate how the use of various AI/ML technologies can be combined to produce a valuable solution to a business problem.

The information, code, models and techniques it contains are illustrations of what an AI-enhanced application first prototype could look like. It is not the definitive way of addressing the stated requirements.

== Disclaimer

This {ic-lab} is an example of what a customer could build using {rhoai}. {rhoai} itself has no specific feature related to Insurance Claim Processing.

This {ic-lab} makes use of large language models (LLM) and image processing models. These models are not included in the {rhoai} product. They are provided as a convenience for this {ic-lab}.

The quality of these models is enough for a prototype. Choosing the right model to use in a production environment is a complex task that requires a lot of experimentation and tuning. This {ic-lab} does not cover this aspect.

== Timetable

This is a tentative timetable for the materials that will be presented.

[width="90%",cols="3,^2,^2,10",options="header"]
|=========================================================
| Name |Duration |Type |Description

|Background |5 | Presentation
a|- We describe what the desired end state looks like.
- Describe overall user experience and underlying architecture.
- Share mockups for better visualization

|Connection and Setup |5 | Hands-On
a|- Attendees get connected
- help validate environment health
- access the playpen project

|LLM |20 | Hands-On
a|- summarization check
- sentiment check
- Model Comparison check and choice
- prompt engineering exercise
- confidence-check pipeline

|Image Processing |20 | Hands-On
a|- car recognition checks
- re-training exercise
- model deployment

|Web App  |20 | Hands-On
a|- deployment
- update
- RAG

|Productization  |5 | Presentation + discussion
a|- What else could we add that would have value?
- What else could we do following the same patterns?

|=========================================================


== Contributing

If you are interested in contributing to this project, consult this GitHub Repo: https://github.com/rh-aiservices-bu/parasol-insurance/[https://github.com/rh-aiservices-bu/parasol-insurance/,window=_blank]
