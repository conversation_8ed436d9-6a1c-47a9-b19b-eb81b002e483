= Pre-Created project and pipeline server
include::_attributes.adoc[]

In order to give you a feel for what "day 2" experience would be like, we have pre-created a Project for you, and started to populate it with various artifacts.

// If you want to learn more about the product and go deeper, the next ("DIY") section will walk you through the steps required to create all this from scratch instead.

== Go to your project

* First, in the {rhoai} Dashboard application, navigate to the Data Science projects menu on the left:
+
[.bordershadow]
image::02/02-02-ds-proj-nav.png[width=70%]

* Then, open the project called {user}.
+
[.bordershadow]
image::02/02-02-open-project.png[]

* Inside the project, by clicking on the different tabs, you should see a few items that already have been pre-created or configured for you:
+
[.bordershadow]
image::02/02-02-pre-created-components.png[]

* These components are as follows:
1. Workbenches: a workbench has already been created - this is the environment that you can experiment and train models in.
2. Pipelines: a pipeline server has already been configured so you can import or run data science pipelines right away. At the moment, it is empty, but we will populate it with a pipeline in a next section.
3. Models: the models panel is empty at the moment.
4. Cluster storage: this is where you find a persistent storage for your workbench and one for the pipeline we will create later.
5. Connections: it contains all the information needed to connect with an existing S3 storage. We use it to store models and pipeline artifacts.
6. Permissions: this is where you can manage the permissions for the project.
