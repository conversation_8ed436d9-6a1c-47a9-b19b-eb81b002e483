= Creating your project and pipeline server
include::_attributes.adoc[]

IMPORTANT: If you've used the auto-created project, you do not need to perform this section. Only do so if you are interested in how a project and pipeline server are created.

As a preliminary step, each of you is going to:

. Create a Data Science project:
** this will help keep your things together

. Create a Data Connection:
** we need that for the pipeline server to store its artifacts

. Deploy a Data Science Pipeline Server:
** we will need one, and it's better to create it from the start

. Launch a Workbench:
** we will use it to review content and notebooks

. Clone the git repo into your Workbench:
** this contains all the code from the prototype

The instructions below will guide you through these steps. Follow them carefully.

== Create a project

* First, in the {rhoai} Dashboard application, navigate to the Data Science Projects menu on the left:
+
[.bordershadow]
image::02/02-02-ds-proj-nav.png[]

* Create a project with the **same name** as your user id
** You have been assigned a unique user ID:  `{user}`
** You need to now create a project with the exact same name: `{user}`
+
IMPORTANT: Your assigned user is {user}. Don't mess that up or things will break later on

* Leave the resource name unchanged
* Optionally, enter your first and last name in the description of the project.
* It should look like this:
+
[.bordershadow]
image::02/02-02-create-project.png[]
+
IMPORTANT: It should **NOT** be `userX` like in the screenshot. (for you, `X` should be a number instead)

== Create a Data Connection for the pipeline server

* We have deployed an instance of Minio in the cluster to act as a simple Object Storage for our purposes.
* You will need to **Add data connection** that points to it.
+
[.bordershadow]
image::02/02-02-add-dc.png[]

* Here is the information you need to enter:
** Name:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
Shared Minio - pipelines
** Access Key:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-user}
** Secret Key:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-pass}
** Endpoint:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-endpoint}
** Region:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
none
** Bucket:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{user}
+
IMPORTANT: Once again, the bucket you will use has to match with the user ID you were provided

* The result should look like:
+
[.bordershadow]
image::02/data-connection.png[]

== Create a Pipeline Server

It is highly recommended to create your pipeline server before creating a workbench. So let's do that now!

* In your Data Science Project (DSP), click on **Configure pipeline Server**
+
[.bordershadow]
image::02/02-02-pipelineserver01.png[]

* Select the Key Drop-Down with the option of *"Populate the form with credentials from your selected data connection"* using the Data Connection created earlier (**Shared Minio - pipelines**) and click the **Configure pipeline server** button:
+
[.bordershadow]
image::02/02-02-pipelineserver02.png[]

* When your pipeline server is ready, your screen will look like the following:
+
[.bordershadow]
image::02/02-02-pipelineserver03.png[]

At this point, your pipeline server is ready and deployed.

IMPORTANT: You need to **wait** until that screen is ready. If it's still spinning, wait for it to complete. If you continue and create your workbench **before** the pipeline server is ready, your workbench will not be able to submit Data Science pipelines to it.
