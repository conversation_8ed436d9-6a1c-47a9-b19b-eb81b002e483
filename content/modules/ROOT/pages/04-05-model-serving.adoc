= Model Serving
include::_attributes.adoc[]

. At this point, we need to deploy the model into {rhoai-short} model serving.
. We will create another Data Connection...
.. With almost identical information
.. But we will change the bucket name from `{user}` to `models`

== Create a Connection

* In your Data Science project, on the Connections tab, click on **Create connection** to create one that refers to the shared storage (Minio) where a copy of the model is stored.
+
[.bordershadow]
image::04/04-add-data-connection.png[]

* Select **S3 compatible object storage - v1** option.

[.bordershadow]
image::04/04-add-data-connection-2.png[]

* Here is the info you need to enter:
** Name:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
Shared Minio - model
** Access Key:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-user}
** Secret Key:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-pass}
** Endpoint:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
{minio-endpoint}
** Region:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
none
** Bucket:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
models

* The result should look like:
+
// [.bordershadow]
image::04/model-data-connection.png[model connection]
image::04/model-data-connection-2.png[model connection]

== Create a Model Server

In your project, select the **Models** tab to create a model server:

* In the **Multi-model serving platform** type of model, the one on the right:
+
[.bordershadow]
image::04/add-model-server.png[]

* Then click **Add model server**:
+
[.bordershadow]
image::04/add-model-server-2.png[width=75%]


* Here is the info you need to enter:

** Model server name:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
My first Model Server
** Serving runtime:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
OpenVINO Model Server
** Number of model server replicas to deploy:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
1
** Model server size
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
Standard
** Accelerator
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
None
** Model route
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
unchecked
** Token authorization
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
unchecked


* The result should look like:
+
[.bordershadow]
image::04/add-model-server-config.png[]

* You can click on **Add** to create the model server.

== Deploy the Model

Still in your project, on the **Models** tab, under **Models and model servers**:

* Click **Deploy model**:
+
[.bordershadow]
image::04/select-deploy-model.png[]

* Here is the information you will need to enter:

** Model name:
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
My first Model
** Model server
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
My first Model Server (pre-filled)
** Model server - Model framework
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
onnx-1
** Existing data connection - Name
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
Shared Minio - model
** Existing data connection - Path
[.lines_space]
[.console-input]
[source, text]
[subs=attributes+]
accident/

* The result should look like:
+
[.bordershadow]
image::04/deploy-a-model.png[]

* Click on **Deploy**.
* If the model is successfully deployed you will see its status as green after 15 to 30 seconds.
+
[.bordershadow]
image::04/model-deployed-success.png[]

We will now confirm that the model is indeed working by querying it!

== Querying the served Model

Once the model is served, we can use it as an endpoint that can be queried. We'll send a request to it, and get a result. And unlike our earlier notebook-based version, this applies to anyone working within our cluster. This could either be colleagues, or applications.

* First, we need to get the URL of the model server.
* To do this, click on the **Internal Service** link under the **Inference endpoint** column.
* In the popup, you will see a few URLs for our model server.
+
[.bordershadow]
image::04/inference-url.png[]

* Note or copy the **RestUrl**, which should be something like `\http://modelmesh-serving.{user}:8008`

We will now use this URL to query the model.

- In your running workbench, navigate to the folder `parasol-insurance/lab-materials/04`.
- Look for (and open) the notebook called `04-05-model-serving.ipynb`.
- Execute the cells of the notebook, and ensure you understand what is happening.

