= Accident Recognition
include::_attributes.adoc[]

== Introduction

Now that we have retrained our model we can test it against some sample images.

We have converted our model to onnx format and placed a copy within an S3 bucket. We will test this version against some sample test images.

Using the re-trained model, we will see that we are able to identify a severe car crash with 88% certainty, like in the below picture:

[.bordershadow]
image::04/retrained-model-results.png[retrained modelresults]

== (Optional) Detailed code and execution

If you want to dig deeper into this section, follow the instructions below. If you are pressed for time, you can skip to the next section.

- In your workbench, navigate to the folder `parasol-insurance/lab-materials/04`.
- Look for (and open) the notebook called `04-04-accident-recog.ipynb`
- Execute the cells of the notebook.
