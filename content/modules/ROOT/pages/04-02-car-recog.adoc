= Car recognition
include::_attributes.adoc[]

== Introduction

In our last notebook we confirmed that the YOLO model could identify cars in a photograph.

[.bordershadow]
image::04/sample-car-image.png[car image]

We discovered that YOLO is able to detect multiple cars in an image. However, we did not see which cars were identified.

[.bordershadow]
image::04/multiple-car-images.png[multiple cars]

[.bordershadow]
image::04/model-prediction-results.png[predict cars]

In the above image, the yolo model identified 17 cars and 1 truck.

Therefore we need to write some code that will draw boxes around the cars identified by the YOLO model.

[.bordershadow]
image::04/box-identified-cars.png[identify cars]

== (Optional) Detailed code and execution

If you want to dig deeper into this section, follow the instructions below. If you are pressed for time, you can skip to the next section.

- In your running workbench, navigate to the folder `parasol-insurance/lab-materials/04`.
- Look for (and open) the notebook called `04-02-car-recognition.ipynb`
- Execute the cells of the notebook, and ensure you understand what is happening
