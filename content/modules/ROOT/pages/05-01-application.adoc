= Application Overview
include::_attributes.adoc[]

== Architecture

Here is a simplified architecture diagram of the application:

[.bordershadow]
image::05/application-architecture.drawio.svg[]


The different components are:

- The **frontend**: the application itself, developed using the Patternfly framework (React, Typescript), running in the browser of the user.
- The **backend**: a Python FastAPI application, running in a container on {ocp}. It handles the communication with the database, the LLM, and the Object storage. It handles the communication with the frontend by exposing a REST API.
- The **database**: a PostgreSQL database, running in a container on {ocp}. It stores the claims.
- The **LLM**: the language model used to summarize and extract information from the claims, and powering the chatbot. It is consumed by the backend through its API.
- The **VectorDB**: the vector database used to store documents for retrieval in the RAG part. It's based on Milvus.
- The **Object storage**: an S3-compatible object storage. It stores the claim images.

== Application Code

If you want to have a look at the code of the application, you can do it directly from {rhoai}!

- Close the different Jupyter tabs that may still be opened in your browser.
- Go back to the {rhoai} Dashboard.
- Stop your Workbench by clicking **Stop**.

+
[.bordershadow]
image::05/05-toggle-off-workbench.png[]
[.bordershadow]
image::05/05-confirm-turn-off.png[]


- You should probably give it 5 or 10 seconds to fully stop.
- Edit the Workbench configuration by clicking the 3 vertical dots on the right, then choose the **Edit workbench** button.
+
[.bordershadow]
image::05/stop-workbench.png[]


- In the `image selection` section, change the image to `CUSTOM - Code Server for {ic}`:
+
[.bordershadow]
image::05/05-change-to-vscode.png[]

- Then click on `Update workbench` at the bottom of the page:
+
[.bordershadow]
image::05/update-workbench.png[]

- You can now start your Workbench again by using the toggle.
- Once the Workbench is started, click the name of the workbench to open.
- You will be redirected to the VSCode IDE (Code Server is the open source implementation of VSCode).
- After logging in and accepting the authorization, VSCode will open.
- The first time you connect to it, you must indicate that you trust the workspace.
- Click on the **Yes, I trust the authors** button.
+
[.bordershadow]
image::05/trust-vscode.png[]

* You can also close the Welcome tab, and you are in a familiar VSCode environment.
* Your Persistent Volume has been automatically reconnected to this new environment.
** Therefore, all the data you created in the Jupyter environment is still available
** You can see more files because VSCode displays hidden files by default, which Jupyter does not do.
* If you want, you can now explore the code of the application, in the `parasol-insurance/app` folder:
+
[.bordershadow]
image::05/app-code.png[]

Of course, this is a complete VSCode environment, so you can configure it in the same way as you would do for a local VSCode installation. You can install extensions that will be persisted when you stop and restart the Workbench, you can configure your own keybindings, etc.

The application has already been built as a container image that we are now ready to deploy using GitOps. You can head to the next section.