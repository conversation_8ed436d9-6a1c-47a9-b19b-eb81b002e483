<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than draw.io -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="711px" height="312px" viewBox="-0.5 -0.5 711 312" content="&lt;mxfile scale=&quot;1&quot; border=&quot;10&quot; host=&quot;app.diagrams.net&quot; modified=&quot;2024-05-03T11:14:31.069Z&quot; agent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0&quot; version=&quot;24.3.1&quot; etag=&quot;w809ERbVLCJSE94VXRnF&quot; type=&quot;device&quot;&gt;&#xA;  &lt;diagram id=&quot;CeySEU4j7Dbo9Kzd_YeK&quot; name=&quot;Page-1&quot;&gt;&#xA;    &lt;mxGraphModel dx=&quot;1136&quot; dy=&quot;603&quot; grid=&quot;1&quot; gridSize=&quot;10&quot; guides=&quot;1&quot; tooltips=&quot;1&quot; connect=&quot;1&quot; arrows=&quot;1&quot; fold=&quot;1&quot; page=&quot;1&quot; pageScale=&quot;1&quot; pageWidth=&quot;850&quot; pageHeight=&quot;1100&quot; math=&quot;0&quot; shadow=&quot;0&quot;&gt;&#xA;      &lt;root&gt;&#xA;        &lt;mxCell id=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;1&quot; parent=&quot;0&quot; /&gt;&#xA;        &lt;mxCell id=&quot;14&quot; value=&quot;&quot; style=&quot;rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;270&quot; width=&quot;420&quot; height=&quot;290&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;2&quot; value=&quot;LLM Server&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;530&quot; y=&quot;79&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;3&quot; value=&quot;Claims Database&amp;lt;br&amp;gt;(PostgreSQL)&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;530&quot; y=&quot;149&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;4&quot; value=&quot;Application Backend&amp;lt;br&amp;gt;(Python)&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;290&quot; y=&quot;116&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;12&quot; style=&quot;edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=classic;startFill=1;&quot; parent=&quot;1&quot; source=&quot;5&quot; target=&quot;4&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;5&quot; value=&quot;Application Frontend&amp;lt;br&amp;gt;(React)&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;70&quot; y=&quot;116&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;6&quot; value=&quot;Claims Images&amp;lt;br&amp;gt;(S3 Storage)&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;530&quot; y=&quot;219&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;7&quot; value=&quot;&quot; style=&quot;verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.user;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry y=&quot;121&quot; width=&quot;47.5&quot; height=&quot;50&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;9&quot; style=&quot;edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1.001;entryY=0.402;entryDx=0;entryDy=0;startArrow=classic;startFill=1;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;2&quot; target=&quot;4&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;370&quot; y=&quot;76&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;370&quot; y=&quot;126&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;10&quot; style=&quot;edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;startArrow=classic;startFill=1;entryX=1.003;entryY=0.601;entryDx=0;entryDy=0;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;3&quot; target=&quot;4&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;540&quot; y=&quot;86&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;430&quot; y=&quot;150&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;11&quot; style=&quot;edgeStyle=none;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1.002;entryY=0.803;entryDx=0;entryDy=0;startArrow=classic;startFill=1;entryPerimeter=0;&quot; parent=&quot;1&quot; source=&quot;6&quot; target=&quot;4&quot; edge=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;550&quot; y=&quot;96&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;450&quot; y=&quot;166&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;13&quot; value=&quot;&quot; style=&quot;verticalLabelPosition=bottom;html=1;verticalAlign=top;align=center;strokeColor=none;fillColor=#00BEF2;shape=mxgraph.azure.computer;pointerEvents=1;&quot; parent=&quot;1&quot; vertex=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;115&quot; y=&quot;186&quot; width=&quot;50&quot; height=&quot;45&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;JC_1tmvgorXDAOXTYR4M-14&quot; value=&quot;&amp;lt;div&amp;gt;VectorDB&amp;lt;/div&amp;gt;&amp;lt;div&amp;gt;(Milvus)&amp;lt;br&amp;gt;&amp;lt;/div&amp;gt;&quot; style=&quot;shape=ext;double=1;rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;&quot; vertex=&quot;1&quot; parent=&quot;1&quot;&gt;&#xA;          &lt;mxGeometry x=&quot;530&quot; y=&quot;13&quot; width=&quot;140&quot; height=&quot;60&quot; as=&quot;geometry&quot; /&gt;&#xA;        &lt;/mxCell&gt;&#xA;        &lt;mxCell id=&quot;JC_1tmvgorXDAOXTYR4M-15&quot; value=&quot;&quot; style=&quot;endArrow=classic;startArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1.003;exitY=0.198;exitDx=0;exitDy=0;exitPerimeter=0;&quot; edge=&quot;1&quot; parent=&quot;1&quot; source=&quot;4&quot; target=&quot;JC_1tmvgorXDAOXTYR4M-14&quot;&gt;&#xA;          &lt;mxGeometry width=&quot;50&quot; height=&quot;50&quot; relative=&quot;1&quot; as=&quot;geometry&quot;&gt;&#xA;            &lt;mxPoint x=&quot;430&quot; y=&quot;127&quot; as=&quot;sourcePoint&quot; /&gt;&#xA;            &lt;mxPoint x=&quot;480&quot; y=&quot;77&quot; as=&quot;targetPoint&quot; /&gt;&#xA;          &lt;/mxGeometry&gt;&#xA;        &lt;/mxCell&gt;&#xA;      &lt;/root&gt;&#xA;    &lt;/mxGraphModel&gt;&#xA;  &lt;/diagram&gt;&#xA;&lt;/mxfile&gt;&#xA;"><defs/><g><g><rect x="280" y="10" width="420" height="290" rx="43.5" ry="43.5" fill="#f8cecc" stroke="#b85450" pointer-events="all"/></g><g><rect x="540" y="89" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="542" y="91" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 119px; margin-left: 543px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">LLM Server</div></div></div></foreignObject><text x="610" y="123" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">LLM Server</text></switch></g></g><g><rect x="540" y="159" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="542" y="161" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 189px; margin-left: 543px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Claims Database<br />(PostgreSQL)</div></div></div></foreignObject><text x="610" y="193" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Claims Database...</text></switch></g></g><g><rect x="300" y="126" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="302" y="128" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 156px; margin-left: 303px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application Backend<br />(Python)</div></div></div></foreignObject><text x="370" y="160" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Application Backend...</text></switch></g></g><g><path d="M 226.37 156 L 293.63 156" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 221.12 156 L 228.12 152.5 L 226.37 156 L 228.12 159.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 298.88 156 L 291.88 159.5 L 293.63 156 L 291.88 152.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="80" y="126" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="82" y="128" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 156px; margin-left: 83px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Application Frontend<br />(React)</div></div></div></foreignObject><text x="150" y="160" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Application Frontend...</text></switch></g></g><g><rect x="540" y="229" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="542" y="231" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 259px; margin-left: 543px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Claims Images<br />(S3 Storage)</div></div></div></foreignObject><text x="610" y="263" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">Claims Images...</text></switch></g></g><g><path d="M 23.58 145.39 C 23.51 141.61 24.78 137.96 27.11 135.25 C 29.44 132.55 32.63 131.01 35.96 131 C 43.4 131.08 49.47 137.48 49.74 145.54 C 50.02 149.24 48.9 152.91 46.63 155.69 C 44.37 158.46 41.16 160.1 37.75 160.23 C 30.22 160.39 23.94 153.81 23.58 145.39 Z M 10 181 C 10.3 173.97 12.91 167.42 17.21 162.94 C 20.62 159.56 25.37 159.36 28.95 162.44 C 31 163.95 33.24 165.17 35.62 166.06 C 38.06 167.12 40.74 166.94 43.08 165.56 C 44.36 164.94 45.5 164.04 46.41 162.94 C 47.31 161.86 48.88 161.64 50.04 162.44 C 52.09 163.81 53.92 166.24 55.36 169.48 C 56.75 173.16 57.48 177.05 57.5 180.99 Z" fill="#00bef2" stroke="none" pointer-events="all"/></g><g><path d="M 533.92 120.89 L 446.22 148.23" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 538.93 119.33 L 533.29 124.76 L 533.92 120.89 L 531.21 118.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 441.21 149.79 L 446.85 144.36 L 446.22 148.23 L 448.93 151.05 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 533.85 187.34 L 446.57 163.72" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 538.92 188.71 L 531.25 190.26 L 533.85 187.34 L 533.08 183.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 441.5 162.35 L 449.17 160.8 L 446.57 163.72 L 447.34 167.56 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><path d="M 535.15 254.87 L 445.13 178.31" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 539.15 258.28 L 531.55 256.41 L 535.15 254.87 L 536.08 251.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 441.13 174.9 L 448.73 176.77 L 445.13 178.31 L 444.2 182.11 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><g><rect x="125" y="196" width="50" height="45" fill="none" stroke="none" pointer-events="all"/><path d="M 127.33 231.55 C 126.66 231.49 126.05 231.17 125.62 230.66 C 125.2 230.16 125 229.51 125.07 228.86 L 125.07 198.51 C 125.17 197.25 126.15 196.24 127.43 196.06 L 172.14 196.06 C 173.61 196 174.87 197.08 175 198.51 L 174.95 229.35 C 174.66 230.65 173.5 231.59 172.14 231.65 L 156.46 231.65 L 155.76 234.44 C 155.6 235.08 155.71 235.76 156.07 236.33 C 156.43 236.89 157.01 237.29 157.67 237.43 C 159.19 237.93 160.79 238.15 162.39 238.06 L 165.31 238.06 L 165.31 241 L 132.71 241 L 132.71 238.06 L 136.17 238.06 C 137.99 238.07 139.79 237.78 141.5 237.18 C 142.84 236.87 143.67 235.55 143.35 234.24 L 142.9 231.65 Z M 128.79 228.03 L 171.13 228.03 L 171.13 199.83 L 128.79 199.83 Z" fill="#00bef2" stroke="none" pointer-events="all"/></g><g><rect x="540" y="23" width="140" height="60" rx="9" ry="9" fill="#f5f5f5" stroke="#666666" pointer-events="all"/><rect x="542" y="25" width="136" height="56" rx="8.4" ry="8.4" fill="#f5f5f5" stroke="#666666" pointer-events="all"/></g><g><g transform="translate(-0.5 -0.5)"><switch><foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 134px; height: 1px; padding-top: 53px; margin-left: 543px;"><div style="box-sizing: border-box; font-size: 0px; text-align: center;" data-drawio-colors="color: #333333; "><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div>VectorDB</div><div>(Milvus)<br /></div></div></div></div></foreignObject><text x="610" y="57" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">VectorDB...</text></switch></g></g><g><path d="M 445.27 133.75 L 535.15 57.13" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 441.27 137.15 L 444.33 129.95 L 445.27 133.75 L 448.87 135.28 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 539.15 53.73 L 536.09 60.93 L 535.15 57.13 L 531.55 55.6 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>