name: modules
title: Navigation
version: ~
nav:
  - modules/ROOT/nav.adoc

asciidoc:
  attributes:
    release-version: dev
    page-pagination: true
    experimental:
    numbered:
    lab_name: "Parasol Insurance AI Workshop"
    guid: my-guid
    ssh_user: lab-user
    ssh_password: lab-user
    ssh_command: ssh lab-user@bastion.{guid}.example.opentlc.com
    minio-user: minio
    minio-pass: minio-parasol
    minio-endpoint: http://minio.ic-shared-minio.svc.cluster.local:9000/
    git-clone-repo-url: https://github.com/rh-aiservices-bu/parasol-insurance.git
    git-clone-repo-branch: dev
    user: userX
    password: openshift
    openshift_console_url: https://PLACEHOLDER-URL.com/
    openshift_cluster_ingress_domain: PLACEHOLDER-URL.com
    rhoai_dashboard_url: https://rhods-dashboard-redhat-ods-applications.{openshift_cluster_ingress_domain}/
    login_command: oc login --insecure-skip-tls-verify=false -u userX -p openshift https://api.MYCLUSTER.com:6443"
    rhoai: "Red Hat OpenShift AI"
    rhoai-short: "RHOAI"
    ocp: "OpenShift Container Platform"
    ocp-short: "OpenShift"
    argocd: "ArgoCD"
    ocp-gitops: "OpenShift GitOps"
    company-name: "Parasol Insurance"
    ic-lab: "lab"
    ic: "{company-name} {ic-lab}"

  extensions:
    - ./content/lib/tab-block.js
    - ./content/lib/remote-include-processor.js
