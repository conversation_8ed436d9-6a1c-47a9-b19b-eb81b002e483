name: Publish to GitHub Pages
on:
  push:
    branches: [main-rhoai-2.13]
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:
concurrency:
  group: pages
  cancel-in-progress: true
# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write
jobs:
  build:
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: main-rhoai-2.13
      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      - name: Install Antora
        run: npm i antora
      - name: Generate Site
        run: npx antora default-site.yml
        # run: npx antora content/default-site.yml
      - name: Setup Pages
        uses: actions/configure-pages@v4
      - name: find where the files are
        run: |
          pwd
          #ls -alR .
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: content/www/
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
