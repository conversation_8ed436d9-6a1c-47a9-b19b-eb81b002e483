apiVersion: batch/v1
kind: Job
metadata:
  name: check-minio-ready-app
  namespace: redhat-ods-operator
  annotations:
    argocd.argoproj.io/hook: PreSync
spec:
  template:
    spec:
      restartPolicy: Never
      containers:
        - name: phase-check
          image: 'image-registry.openshift-image-registry.svc:5000/openshift/tools:latest'
          command:
            - /bin/sh
            - -c
            - |
              while true; do
              echo "Checking Pod status.phase..."
              POD_PHASE=$(oc get pod -n ic-shared-minio -l app=minio -o jsonpath='{.items[0].status.phase}')
              if [ "$POD_PHASE" = "Running" ]; then
                echo "Pod is in Running phase."
                exit 0
              else
                echo "Pod is in $POD_PHASE phase."
              fi
              sleep 5
              done
      serviceAccountName: presync-monitoring-sa
