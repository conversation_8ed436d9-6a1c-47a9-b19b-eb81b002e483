---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: presync-monitoring-sa
  namespace: redhat-ods-operator
  annotations:
    argocd.argoproj.io/sync-wave: "1"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: presync-monitoring-cluster-role
  annotations:
    argocd.argoproj.io/sync-wave: "1"
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["dscinitialization.opendatahub.io"]
  resources: ["dscinitializations"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["datasciencecluster.opendatahub.io"]
  resources: ["datascienceclusters"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["storage.k8s.io"]
  resources: ["storageclasses"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: presync-monitoring-cluster-role-binding
  annotations:
    argocd.argoproj.io/sync-wave: "1"
subjects:
- kind: ServiceAccount
  name: presync-monitoring-sa
  namespace: redhat-ods-operator
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: presync-monitoring-cluster-role
