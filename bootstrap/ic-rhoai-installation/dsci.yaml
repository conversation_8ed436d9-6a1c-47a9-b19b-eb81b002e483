kind: DataScienceCluster
apiVersion: datasciencecluster.opendatahub.io/v1
metadata:
  name: default
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  components:
    codeflare:
      devFlags: {}
      managementState: Managed
    kserve:
      managementState: Managed
      nim:
        managementState: Managed
      rawDeploymentServiceConfig: Headless
      serving:
        ingressGateway:
          certificate:
            type: OpenshiftDefaultIngress
        managementState: Managed
        name: knative-serving
    modelregistry:
      managementState: Managed
      registriesNamespace: rhoai-model-registries
    trustyai:
      devFlags:
        manifests:
          - contextDir: config
            sourcePath: ''
            uri: 'https://api.github.com/repos/trustyai-explainability/trustyai-service-operator/tarball/main'
      managementState: Managed
    ray:
      devFlags: {}
      managementState: Managed
    kueue:
      managementState: Managed
    workbenches:
      managementState: Managed
    dashboard:
      devFlags: {}
      managementState: Managed
    modelmeshserving:
      devFlags: {}
      managementState: Managed
    datasciencepipelines:
      devFlags: {}
      managementState: Managed
    trainingoperator:
      managementState: Removed
