---
apiVersion: v1
kind: ConfigMap
metadata:
  name: milvus-backup-configmap
  annotations:
    argocd.argoproj.io/sync-wave: "1"
  labels:
    app: ic-shared-milvus
    argocd.argoproj.io/managed-by: openshift-gitops
data:
  backup.yaml: >-
    log:
      level: info
      console: true
      file:
        rootPath: "/var/log/milvus-backup/logs"

    http:
      simpleResponse: true

    milvus:
      address: vectordb-milvus.ic-shared-milvus.svc.cluster.local
      port: 19530
      authorizationEnabled: true
      tlsMode: 0
      user: "root"
      password: "Milvus"

    minio:
      storageType: "minio"
      address: minio.ic-shared-minio.svc.cluster.local
      port: 9000
      accessKeyID: minio
      secretAccessKey: minio-parasol
      useSSL: false
      useIAM: false
      iamEndpoint: ""
      bucketName: "milvus-bucket"
      rootPath: "file"      
      backupBucketName: "milvus-backup-bucket"
      backupRootPath: "backup"

    backup:
      maxSegmentGroupSize: 2G

      parallelism: 
        backupCollection: 4
        copydata: 128
        restoreCollection: 2
      
      keepTempFiles: false
      
      gcPause:
        enable: true
        seconds: 7200
        address: http://vectordb-milvus.ic-shared-milvus.svc.cluster.local:9091
      