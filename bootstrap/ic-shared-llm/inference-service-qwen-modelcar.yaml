apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    openshift.io/display-name: qwen
    serving.knative.openshift.io/enablePassthrough: 'true'
    sidecar.istio.io/inject: 'true'
    sidecar.istio.io/rewriteAppHTTPProbers: 'true'
    argocd.argoproj.io/sync-wave: "2"
    serving.kserve.io/deploymentMode: RawDeployment
    argocd.argoproj.io/compare-options: IgnoreExtraneous
    argocd.argoproj.io/sync-options: Prune=false
  name: qwen
  namespace: ic-shared-llm
  labels:
    opendatahub.io/dashboard: 'true'
spec:
  predictor:
    maxReplicas: 1
    minReplicas: 1
    model:
      args:
        - '--port=8080'
        - '--model=/mnt/models'
        - '--served-model-name=qwen2.5'
        - '--max-model-len=2048'
        - '--tensor-parallel-size=1'
      modelFormat:
        name: vLLM
      name: ''
      resources:
        limits:
          cpu: '4'
          memory: 6Gi
        requests:
          cpu: '4'
          memory: 5Gi
      runtime: vllm-cpu
      storageUri: oci://quay.io/rh-aiservices-bu/qwen2.5-0.5b-quantized.w8a8-modelcar:0.0.1
    tolerations:
      - effect: NoSchedule
        key: nvidia.com/gpu
        operator: Exists