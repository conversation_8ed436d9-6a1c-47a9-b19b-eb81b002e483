---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  labels:
    app: minio
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: minio
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        args:
        - minio server /data --console-address :9090
        command:
        - /bin/bash
        - -c
        image: quay.io/minio/minio:RELEASE.2023-06-19T19-52-50Z
        envFrom:
        - secretRef:
            name: minio-root-user
        ports:
        - containerPort: 9000
          protocol: TCP
          name: api
        - containerPort: 9090
          protocol: TCP
          name: console
        resources:
          requests:
            cpu: 200m
            memory: 1Gi
          limits:
            cpu: 2
            memory: 2Gi
        volumeMounts:
        - mountPath: /data
          name: minio
      volumes:
      - persistentVolumeClaim:
          claimName: minio
        name: minio
