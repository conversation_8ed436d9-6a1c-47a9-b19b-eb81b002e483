---
apiVersion: batch/v1
kind: Job
metadata:
  name: create-data-connection
  # generateName: create-dc-
  annotations:
    argocd.argoproj.io/sync-wave: "2"
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  backoffLimit: 4
  template:
    spec:
      serviceAccount: minio-manage
      serviceAccountName: minio-manage
      containers:
      - name: create-data-connection
        image: image-registry.openshift-image-registry.svc:5000/openshift/tools:latest
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          echo -n "Waiting for minio-root-user to exist"
          while [ -z "$(oc get secret -n ic-shared-minio minio-root-user -oname 2>/dev/null)" ]; do
            echo -n '.'
            sleep 1
          done; echo
          id=$(oc get secret -n ic-shared-minio minio-root-user -ogo-template='{{.data.MINIO_ROOT_USER|base64decode}}')
          secret=$(oc get secret -n ic-shared-minio minio-root-user -ogo-template='{{.data.MINIO_ROOT_PASSWORD|base64decode}}')
          echo "Minio Console : https://$(oc get route -n ic-shared-minio minio-console -ojsonpath='{.status.ingress[0].host}')/ "
          echo "Minio user: ${id}"
          echo "Minio pass: ${secret}"
          echo "Internal service url: http://minio.ic-shared-minio.svc.cluster.local:9000/"
          cat << EOF | oc apply -f-
          apiVersion: v1
          kind: Secret
          metadata:
            name: aws-connection-minio
            labels:
              opendatahub.io/dashboard: "true"
              opendatahub.io/managed: "true"
            annotations:
              opendatahub.io/connection-type: s3
              openshift.io/display-name: MinIO
          type: Opaque
          stringData:
            AWS_ACCESS_KEY_ID: ${id}
            AWS_SECRET_ACCESS_KEY: ${secret}
            AWS_DEFAULT_REGION: us
            AWS_S3_ENDPOINT: http://minio.ic-shared-minio.svc:9000
            AWS_S3_BUCKET: bucket-placeholder
          EOF
      restartPolicy: Never
