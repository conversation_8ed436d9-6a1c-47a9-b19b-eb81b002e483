---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: minio-console
  labels:
    app: minio
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  port:
    targetPort: console
  tls:
    insecureEdgeTerminationPolicy: Redirect
    termination: edge
  to:
    kind: Service
    name: minio
    weight: 100
  wildcardPolicy: None
---
apiVersion: route.openshift.io/v1
kind: Route
metadata:
  name: minio-s3
  labels:
    app: minio
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  port:
    targetPort: api
  tls:
    insecureEdgeTerminationPolicy: Redirect
    termination: edge
  to:
    kind: Service
    name: minio
    weight: 100
  wildcardPolicy: None
