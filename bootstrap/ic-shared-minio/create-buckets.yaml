---
apiVersion: batch/v1
kind: Job
metadata:
  name: create-buckets
  annotations:
    argocd.argoproj.io/sync-wave: "3"
    argocd.argoproj.io/hook: Sync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  backoffLimit: 4
  template:
    spec:
      serviceAccount: minio-manage
      serviceAccountName: minio-manage
      initContainers:
      - name: wait-for-minio
        image: image-registry.openshift-image-registry.svc:5000/openshift/tools:latest
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          echo -n "Waiting for minio pod in ic-shared-minio namespace"
          while [ -z "$(oc get pod -n ic-shared-minio -l app=minio -o name 2>/dev/null)" ]; do
              echo -n '.'
              sleep 1
          done
          echo "Minio pod is running in ic-shared-minio namespace"
      containers:
      - name: add-model
        image: image-registry.openshift-image-registry.svc:5000/redhat-ods-applications/s2i-generic-data-science-notebook:2025.1
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          curl -LO https://rhods-public.s3.amazonaws.com/demo-models/ic-models/accident/accident_detect.onnx

          cat << 'EOF' | python3
          import boto3, os

          s3 = boto3.client("s3",
                            endpoint_url=os.getenv("AWS_S3_ENDPOINT"),
                            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"))

          # Create user buckets
          for i in range(1, 101):
              bucket_name = f"user{i}"
              if bucket_name not in [bu["Name"] for bu in s3.list_buckets()["Buckets"]]:
                  s3.create_bucket(Bucket=bucket_name)

          # Create the models bucket
          models_bucket_name = "models"
          if models_bucket_name not in [bu["Name"] for bu in s3.list_buckets()["Buckets"]]:
              s3.create_bucket(Bucket=models_bucket_name)

          # uploading to minio
          filename = "accident_detect.onnx"
          with open(filename, "rb") as f:
              s3.upload_fileobj(f, "models", f'accident/{filename}')

          EOF
        envFrom:
        - secretRef:
            name: aws-connection-minio
      restartPolicy: Never
