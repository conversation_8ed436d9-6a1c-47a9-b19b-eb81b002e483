---
apiVersion: batch/v1
kind: Job
metadata:
  name: create-minio-root-user
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  backoffLimit: 4
  template:
    spec:
      serviceAccount: minio-manage
      serviceAccountName: minio-manage
      containers:
      - name: create-minio-root-user
        image: image-registry.openshift-image-registry.svc:5000/openshift/tools:latest
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          if [ -n "$(oc get secret minio-root-user -oname 2>/dev/null)" ]; then
            echo "Secret already exists. Skipping." >&2
            exit 0
          fi
          genpass() {
              < /dev/urandom tr -dc _A-Z-a-z-0-9 | head -c"${1:-32}"
          }
          #id=$(genpass 16)
          #secret=$(genpass)

          # hard-code id and secret instead
          id="minio"
          secret="minio-parasol"

          cat << EOF | oc apply -f-
          apiVersion: v1
          kind: Secret
          metadata:
            name: minio-root-user
          type: Opaque
          stringData:
            MINIO_ROOT_USER: ${id}
            MINIO_ROOT_PASSWORD: ${secret}
          EOF
      restartPolicy: Never
