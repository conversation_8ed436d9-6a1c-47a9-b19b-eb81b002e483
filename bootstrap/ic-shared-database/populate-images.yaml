---
apiVersion: batch/v1
kind: Job
metadata:
  name: populate-images
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  backoffLimit: 4
  template:
    spec:
      initContainers:
      - name: wait-for-minio
        image: busybox:1.28
        command: ['sh', '-c', 'until nc -z -v -w30 $MINIO_ENDPOINT 9000; do echo "Waiting for Minio connection..."; sleep 2; done;']
        env:
        - name: MINIO_ENDPOINT
          value: minio.ic-shared-minio.svc.cluster.local
      containers:
      - name: add-images-to-bucket
        image: image-registry.openshift-image-registry.svc:5000/redhat-ods-applications/s2i-generic-data-science-notebook:2025.1
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          BRANCH_NAME="dev"
          cd /opt/app-root/src
          git clone https://github.com/rh-aiservices-bu/parasol-insurance.git && cd parasol-insurance && git checkout $BRANCH_NAME

          cat << 'EOF' | python3
          import boto3, os, botocore

          s3 = boto3.client("s3",
                            endpoint_url=os.getenv("AWS_S3_ENDPOINT"),
                            aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                            aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"))

          # Create image bucket
          bucket_name = "claim-images"
          try:
            s3.head_bucket(Bucket=bucket_name)
          except botocore.exceptions.ClientError as e:
            if e.response['Error']['Code'] == '404':
              s3.create_bucket(Bucket=bucket_name)

          # Upload original images to minio
          for filename in os.listdir("/opt/app-root/src/parasol-insurance/bootstrap/ic-shared-database/images/original_images"):
              with open(f"/opt/app-root/src/parasol-insurance/bootstrap/ic-shared-database/images/original_images/{filename}", "rb") as f:
                  s3.upload_fileobj(f, bucket_name, f"original_images/{filename}")

          # Upload processed images to minio
          for filename in os.listdir("/opt/app-root/src/parasol-insurance/bootstrap/ic-shared-database/images/processed_images"):
              with open(f"/opt/app-root/src/parasol-insurance/bootstrap/ic-shared-database/images/processed_images/{filename}", "rb") as f:
                  s3.upload_fileobj(f, bucket_name, f"processed_images/{filename}")

          EOF
        envFrom:
        - secretRef:
            name: secret-minio
      restartPolicy: Never
