---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: project-creator
  namespace: ic-user-projects
  annotations:
    argocd.argoproj.io/sync-wave: "1"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: project-creator-binding
  annotations:
    argocd.argoproj.io/sync-wave: "1"
subjects:
- kind: ServiceAccount
  name: project-creator
  namespace: ic-user-projects
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: route-reader
  annotations:
    argocd.argoproj.io/sync-wave: "1"
rules:
- apiGroups: ["route.openshift.io"]
  resources: ["routes"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
---