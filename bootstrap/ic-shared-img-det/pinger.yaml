---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: model-pinger
  labels:
    app: model-pinger
spec:
  replicas: 1
  revisionHistoryLimit: 3
  selector:
    matchLabels:
      app: model-pinger
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 100%
  template:
    metadata:
      labels:
        app: model-pinger
    spec:
      containers:
      - name: pinger
        image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-small-datascience:1.0
        command:
        - python
        - -u
        - /ping_model.py
        env:
        - name: MM_SERVING_HOST
          value: modelmesh-serving:8008
        - name: MM_MODEL_NAME
          # value: fraud-4
          value: fraud-latest
        - name: SLEEP_TIME
          value: "5"
        resources:
          limits:
            cpu: "0.2"
            memory: 256Mi
        volumeMounts:
        - name: pinger-config
          subPath: ping_model.py
          mountPath: /ping_model.py
      volumes:
      - name: pinger-config
        configMap:
          name: model-pinger

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: model-pinger
data:
  ping_model.py: |
    import os
    import time
    from datetime import datetime
    import random

    # MM_SERVING_HOST = os.environ.get("MM_SERVING_HOST", "modelmesh-serving:8008")
    # MM_MODEL_NAME = os.environ.get("MM_MODEL_NAME", "fraud-latest")

    # # deployed_model_name = "fraud-latest"
    # # rest_url = "http://modelmesh-serving:8008"
    # deployed_model_name = MM_MODEL_NAME
    # rest_url = f"http://{MM_SERVING_HOST}"
    # infer_url = f"{rest_url}/v2/models/{deployed_model_name}/infer"
    # print(infer_url)

    # import requests

    # def rest_request(data):
    #     json_data = {
    #         "inputs": [
    #             {
    #                 "name": "dense_input",
    #                 "shape": [1, 5],
    #                 "datatype": "FP32",
    #                 "data": data
    #             }
    #         ]
    #     }

    #     response = requests.post(infer_url, json=json_data)
    #     response_dict = response.json()
    #     return response_dict['outputs'][0]['data']


    # MM_SERVING_HOST = os.environ.get("MM_SERVING_HOST", "modelmesh-serving:8033")
    # MM_MODEL_NAME = os.environ.get("MM_MODEL_NAME", "ansible-model-pinger")

    # SLEEP_TIME = os.environ.get("SLEEP_TIME", "3")
    # SLEEP_TIME = int(SLEEP_TIME)


    # while True:
    #   now = datetime.now()
    #   time_string = now.strftime("%Y-%m-%d %H:%M:%S")

    #   print("--------------------------------------")
    #   print("Sending Request at",time_string)

    #   start = time.time()
    #   try:
    #     random_dist = round(random.uniform(0, 50), 2)
    #     data = [random_dist, 1.9459399775518593, 1.0, 0.0, 0.0]
    #     print("Data sent to model ("+deployed_model_name+"):")
    #     print(data)

    #     # Start the timer
    #     start_time = time.time()

    #     prediction = rest_request(data)

    #     # End the timer
    #     end_time = time.time()

    #     # Calculate the elapsed time in milliseconds
    #     response_time_ms = (end_time - start_time) * 1000

    #      # prediction
    #     print("Likelyhood of fraud")
    #     print(str(prediction[0]*100)+"%")

    #     print("Prediction took ", response_time_ms, "milliseconds")

    #   except Exception as e:
    #     print(f"Errored after {time.time() - start}s: {e}")

    #   time.sleep(SLEEP_TIME)