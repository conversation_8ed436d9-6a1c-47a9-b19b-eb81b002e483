---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: sa-img-det-gitops-setup
  annotations:
    argocd.argoproj.io/sync-wave: "1"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: sa-img-det-gitops-setup-cluster-reader
  annotations:
    argocd.argoproj.io/sync-wave: "1"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: sa-img-det-gitops-setup
  namespace: ic-shared-img-det
