---
apiVersion: batch/v1
kind: Job
metadata:
  name: add-model-job
  annotations:
    argocd.argoproj.io/sync-wave: "3"
spec:
  backoffLimit: 4
  template:
    spec:
      serviceAccount: sa-img-det-gitops-setup
      serviceAccountName: sa-img-det-gitops-setup
      initContainers:
      # - name: wait-for-imagestream
      #   image: image-registry.openshift-image-registry.svc:5000/openshift/tools:latest
      #   imagePullPolicy: IfNotPresent
      #   command: ["/bin/bash"]
      #   args:
      #   - -ec
      #   - |-
      #     echo -n 'Waiting for RHODS to deploy ImageStreams'
      #     while ! oc get istag -n redhat-ods-applications 2>/dev/null | grep -qF s2i-generic-data-science-notebook:2025.1; do
      #       echo -n .
      #       sleep 5
      #     done; echo
      containers:
      - name: add-model
        image: image-registry.openshift-image-registry.svc:5000/redhat-ods-applications/s2i-generic-data-science-notebook:2025.1
        imagePullPolicy: IfNotPresent
        command: ["/bin/bash"]
        args:
        - -ec
        - |-
          # in case we need to add more models, we can do it here too.
          echo -n "not doing anything right now"
          # curl -LO https://ai-on-openshift.io/odh-rhods/img-triton/card.fraud.detection.onnx
          # cat << 'EOF' | python3
          # filename = "card.fraud.detection.onnx"
          # import boto3, os
          # s3 = boto3.client("s3",
          #                   endpoint_url=os.getenv("AWS_S3_ENDPOINT"),
          #                   aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
          #                   aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"))
          # bucket = os.getenv("AWS_S3_BUCKET")
          # if bucket not in [bu["Name"] for bu in s3.list_buckets()["Buckets"]]:
          #   s3.create_bucket(Bucket=bucket)
          # with open(filename, "rb") as f:
          #   s3.upload_fileobj(f, bucket, f'fraud/{filename}')
          # EOF
        envFrom:
        - secretRef:
            name: aws-connection-minio
      restartPolicy: Never
