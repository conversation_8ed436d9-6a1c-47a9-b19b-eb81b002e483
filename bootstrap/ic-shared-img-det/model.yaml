---
apiVersion: serving.kserve.io/v1beta1
kind: InferenceService
metadata:
  annotations:
    argocd.argoproj.io/sync-wave: "4"
    openshift.io/display-name: img-det
    serving.kserve.io/deploymentMode: ModelMesh
  labels:
    name: "img-det"
    opendatahub.io/dashboard: 'true'
  name: "img-det"
spec:
  predictor:
    model:
      modelFormat:
        name: onnx
        version: '1'
      runtime: ovms
      storage:
        key: aws-connection-minio
        path: accident/