"""
Supabase Storage utility for handling file uploads and downloads
"""
import requests
import json
import os
from typing import BinaryIO, Optional

class SupabaseStorage:
    def __init__(self, config, logger):
        self.config = config
        self.logger = logger
        self.base_url = config["SUPABASE_URL"]
        self.service_role_key = config["SUPABASE_SERVICE_ROLE_KEY"]
        self.bucket_name = config.get("STORAGE_BUCKET", "claim-images")
        
        # Headers for API requests
        self.headers = {
            "Authorization": f"Bearer {self.service_role_key}",
            "Content-Type": "application/json"
        }
        
        # Initialize bucket
        self._ensure_bucket_exists()
    
    def _ensure_bucket_exists(self):
        """Create the storage bucket if it doesn't exist"""
        try:
            # Check if bucket exists
            url = f"{self.base_url}/storage/v1/bucket/{self.bucket_name}"
            response = requests.get(url, headers=self.headers)
            
            if response.status_code == 404:
                # Create bucket
                create_url = f"{self.base_url}/storage/v1/bucket"
                bucket_data = {
                    "id": self.bucket_name,
                    "name": self.bucket_name,
                    "public": True,
                    "file_size_limit": 52428800,  # 50MB
                    "allowed_mime_types": ["image/jpeg", "image/png", "image/gif", "image/webp"]
                }
                
                create_response = requests.post(
                    create_url, 
                    headers=self.headers, 
                    json=bucket_data
                )
                
                if create_response.status_code in [200, 201]:
                    self.logger.info(f"✅ Created storage bucket: {self.bucket_name}")
                else:
                    self.logger.warning(f"⚠️ Could not create bucket: {create_response.text}")
            else:
                self.logger.info(f"✅ Storage bucket exists: {self.bucket_name}")
                
        except Exception as e:
            self.logger.warning(f"⚠️ Error checking/creating bucket: {e}")
    
    def upload_file(self, file_path: str, file_content: BinaryIO, content_type: str = "image/jpeg") -> bool:
        """
        Upload a file to Supabase Storage
        
        Args:
            file_path: Path within the bucket (e.g., "images/claim_1_photo.jpg")
            file_content: File content as binary
            content_type: MIME type of the file
            
        Returns:
            bool: True if upload successful, False otherwise
        """
        try:
            url = f"{self.base_url}/storage/v1/object/{self.bucket_name}/{file_path}"
            
            headers = {
                "Authorization": f"Bearer {self.service_role_key}",
                "Content-Type": content_type
            }
            
            response = requests.post(url, headers=headers, data=file_content)
            
            if response.status_code in [200, 201]:
                self.logger.info(f"✅ Uploaded file: {file_path}")
                return True
            else:
                self.logger.error(f"❌ Upload failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Upload error: {e}")
            return False
    
    def get_public_url(self, file_path: str) -> str:
        """
        Get the public URL for a file
        
        Args:
            file_path: Path within the bucket
            
        Returns:
            str: Public URL for the file
        """
        return f"{self.base_url}/storage/v1/object/public/{self.bucket_name}/{file_path}"
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from storage
        
        Args:
            file_path: Path within the bucket
            
        Returns:
            bool: True if deletion successful, False otherwise
        """
        try:
            url = f"{self.base_url}/storage/v1/object/{self.bucket_name}/{file_path}"
            
            response = requests.delete(url, headers=self.headers)
            
            if response.status_code in [200, 204]:
                self.logger.info(f"✅ Deleted file: {file_path}")
                return True
            else:
                self.logger.error(f"❌ Delete failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Delete error: {e}")
            return False
    
    def list_files(self, prefix: str = "") -> list:
        """
        List files in the bucket
        
        Args:
            prefix: Optional prefix to filter files
            
        Returns:
            list: List of file objects
        """
        try:
            url = f"{self.base_url}/storage/v1/object/list/{self.bucket_name}"
            
            params = {}
            if prefix:
                params["prefix"] = prefix
            
            response = requests.post(url, headers=self.headers, json=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"❌ List files failed: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            self.logger.error(f"❌ List files error: {e}")
            return []

    # S3-compatible methods for easy replacement
    def put_object(self, Bucket: str, Key: str, Body: BinaryIO, **kwargs):
        """S3-compatible upload method"""
        return self.upload_file(Key, Body)
    
    def generate_presigned_url(self, ClientMethod: str, Params: dict, ExpiresIn: int = 3600) -> str:
        """S3-compatible URL generation (returns public URL for Supabase)"""
        if ClientMethod == "get_object":
            return self.get_public_url(Params["Key"])
        return ""
