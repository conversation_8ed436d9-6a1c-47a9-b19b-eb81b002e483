""" Backend for Parasol Insurance Claims Processing App """
import hashlib
import logging
import os
import sys
import time
from typing import List
import json

import data_classes
import db_utils
import chatbot
import supabase_storage
import httpx
from app_config import LOG_LEVELS, LOGGING_CONFIG
from dotenv import dotenv_values, load_dotenv
from fastapi import FastAPI, File, HTTPException, Request, UploadFile, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from fastapi.staticfiles import StaticFiles
from uvicorn import run
from fastapi import HTTPException
from starlette.exceptions import HTTPException as StarletteHTTPException

# Load local env vars if present
load_dotenv()

# Initialize logger
logger = logging.getLogger("app")

# Get config
config = {
    **dotenv_values(".env"),  # load shared development variables
    **dotenv_values(".env.secret"),  # load sensitive variables
    **os.environ,  # override loaded values with environment variables
}
logger.info(f'Config: INFERENCE_SERVER_URL={config["INFERENCE_SERVER_URL"]}')

# App creation
app = FastAPI()

origins = ["*"]
methods = ["*"]
headers = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=methods,
    allow_headers=headers
)

# Initialize DB
db = db_utils.Database(config, logger)

# Initialize Supabase Storage
storage = supabase_storage.SupabaseStorage(config, logger)

# Initialize Chatbot
chatbot_instance = chatbot.Chatbot(config, logger)

# Status API
@app.get("/health")
async def health():
    """ Basic status """
    return {"message": "Status:OK"}

@app.get("/api/db/tables")
async def db_list_tables():
    """
    List all the available tables in the Database
    """
    tables = db.list_tables()
    return tables

@app.get("/api/db/claims", response_model = List[data_classes.ClaimBaseInfo])
async def db_list_claims():
    """
    List all the claims
    """
    claims = db.list_claims()
    return claims

@app.get("/api/db/claims/{claim_id}", response_model = data_classes.ClaimFullInfo)
async def db_get_claim_info(claim_id):
    """
    Returns the full content of a claim
    """
    claim_info = db.get_claim_info(claim_id)
    return claim_info

@app.post("/api/db/claims", response_model = data_classes.ClaimBaseInfo)
async def db_create_claim(claim: data_classes.ClaimCreationInfo):
    """
    Creates a new claim
    """
    claim_id = db.create_claim(
        claim.subject,
        claim.body,
        client_name=claim.client_name,
        policy_number=claim.policy_number,
        category=claim.category
    )
    claim_info = db.get_claim_base_info(claim_id)
    return claim_info

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    General file upload endpoint for claim documents
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(file.filename.encode()).hexdigest()
    file_key = f"uploads/{timestamp}_{sha256_hash[:8]}_{file.filename}"

    try:
        # Upload the file to Supabase Storage
        storage.upload_file(file_key, file.file.read(), content_type=file.content_type)

        # Get the public URL
        public_url = storage.get_public_url(file_key)

        return {
            "message": f"{file.filename} uploaded successfully",
            "filename": file.filename,
            "key": file_key,
            "url": public_url
        }
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.post("/api/db/claims/{claim_id}/original_image")
async def db_upload_original_image(claim_id: int, image: UploadFile = File(...)):
    """
    Uploads an original image of a claim
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(image.filename.encode()).hexdigest()
    image_key = f"original_images/{claim_id}_{timestamp}_{sha256_hash[:8]}_{image.filename}"

    # Upload the image to Supabase Storage
    storage.upload_file(image_key, image.file.read(), content_type=image.content_type)
    # Save image info to DB
    db.upload_original_image(claim_id, image.filename, image_key)

    return {"message": f"{image.filename} uploaded as an original image of claim {claim_id}"}

@app.post("/api/db/claims/{claim_id}/processed_image")
async def db_upload_processed_image(claim_id: int, image: UploadFile = File(...)):
    """
    Uploads a processed image of a claim
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(image.filename.encode()).hexdigest()
    image_key = f"processed_images/{claim_id}_{timestamp}_{sha256_hash[:8]}_{image.filename}"

    # Upload the image to Supabase Storage
    storage.upload_file(image_key, image.file.read(), content_type=image.content_type)
    # Save image info to DB
    db.upload_processed_image(claim_id, image.filename, image_key)
    
    return {"message": f"{image.filename} uploaded as a processed image of claim {claim_id}"}

@app.post("/api/db/claims/{claim_id}/summary")
async def db_update_claim_summary(claim_id: int, summary: str = None):
    """
    Updates the summary of a claim or generates one using AI
    """
    if summary is None:
        # Generate summary using AI
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Use the chatbot to generate a summary
        summary_prompt = f"Please provide a concise summary of this insurance claim in 2-3 sentences:\n\n{claim_body}"

        # Generate summary using the chatbot
        summary_parts = []
        for response in chatbot_instance.stream(summary_prompt, ""):
            if response.get("type") == "token":
                summary_parts.append(response.get("token", ""))

        summary = "".join(summary_parts).strip()

    db.update_claim_summary(claim_id, summary)
    return {"message": "Summary generated and uploaded", "summary": summary}

@app.post("/api/db/claims/{claim_id}/time")
async def db_update_claim_time(claim_id: int, time: str = None):
    """
    Updates the time of a claim or extracts it using AI
    """
    if time is None:
        # Extract time using AI
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Use the chatbot to extract time
        time_prompt = f"Extract the date and time when this incident occurred from the insurance claim. If no specific time is mentioned, respond with 'Not specified'. Provide only the date/time information:\n\n{claim_body}"

        # Generate time using the chatbot
        time_parts = []
        for response in chatbot_instance.stream(time_prompt, ""):
            if response.get("type") == "token":
                time_parts.append(response.get("token", ""))

        time = "".join(time_parts).strip()
        if not time:
            time = "Not specified"

    db.update_claim_time(claim_id, time)
    return {"message": "Time extracted and uploaded", "time": time}

@app.post("/api/db/claims/{claim_id}/location")
async def db_update_claim_location(claim_id: int, location: str = None):
    """
    Updates the location of a claim or extracts it using AI
    """
    if location is None:
        # Extract location using AI
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Use the chatbot to extract location
        location_prompt = f"Extract the location where this incident occurred from the insurance claim. If no specific location is mentioned, respond with 'Not specified'. Provide only the location (city, state, or address):\n\n{claim_body}"

        # Generate location using the chatbot
        location_parts = []
        for response in chatbot_instance.stream(location_prompt, ""):
            if response.get("type") == "token":
                location_parts.append(response.get("token", ""))

        location = "".join(location_parts).strip()
        if not location:
            location = "Not specified"

    db.update_claim_location(claim_id, location)
    return {"message": "Location extracted and uploaded", "location": location}

@app.post("/api/db/claims/{claim_id}/sentiment")
async def db_update_claim_sentiment(claim_id: int, sentiment: str = None):
    """
    Updates the sentiment of a claim or analyzes it using AI
    """
    if sentiment is None:
        # Analyze sentiment using AI
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Use the chatbot to analyze sentiment
        sentiment_prompt = f"Analyze the sentiment of this insurance claim. Respond with only one word: 'positive', 'negative', or 'neutral':\n\n{claim_body}"

        # Generate sentiment using the chatbot
        sentiment_parts = []
        for response in chatbot_instance.stream(sentiment_prompt, ""):
            if response.get("type") == "token":
                sentiment_parts.append(response.get("token", ""))

        sentiment = "".join(sentiment_parts).strip().lower()
        # Ensure valid sentiment value
        if sentiment not in ['positive', 'negative', 'neutral']:
            sentiment = 'neutral'

    db.update_claim_sentiment(claim_id, sentiment)
    return {"message": "Sentiment analyzed and uploaded", "sentiment": sentiment}

@app.post("/api/db/claims/{claim_id}/process")
async def process_claim_with_ai(claim_id: int):
    """
    Comprehensive AI processing of claim including email content and documents
    """
    try:
        # Get claim information
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Get uploaded documents
        original_images = claim_info.get("original_images", []) or []

        # Create comprehensive analysis prompt
        analysis_prompt = f"""
        Analyze this insurance claim comprehensively. The claim includes:

        Email Content:
        {claim_body}

        Uploaded Documents: {len(original_images)} files uploaded

        Please provide a detailed analysis including:
        1. Summary of the incident
        2. Key details extracted
        3. Potential concerns or red flags
        4. Recommended next steps
        """

        # Generate comprehensive analysis
        analysis_parts = []
        for response in chatbot_instance.stream(analysis_prompt, ""):
            if response.get("type") == "token":
                analysis_parts.append(response.get("token", ""))

        comprehensive_analysis = "".join(analysis_parts).strip()

        # Trigger individual AI processing endpoints
        await db_update_claim_summary(claim_id)
        await db_update_claim_sentiment(claim_id)
        await db_update_claim_location(claim_id)
        await db_update_claim_time(claim_id)

        return {
            "message": "Comprehensive AI processing completed",
            "analysis": comprehensive_analysis,
            "documents_processed": len(original_images)
        }

    except Exception as e:
        logger.error(f"AI processing failed for claim {claim_id}: {e}")
        raise HTTPException(status_code=500, detail=f"AI processing failed: {str(e)}")

@app.get("/api/images")
async def s3_list_images():
    """
    Returns the list of images
    """
    images = storage.list_files()
    return images

@app.get("/api/images/{image_key:path}")
async def s3_get_image(image_key: str):
    """
    Returns the image with the given key
    """
    # For Supabase Storage, we redirect to the public URL
    public_url = storage.get_public_url(image_key)
    return {"url": public_url}

@app.websocket("/ws/query")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()

    while True:
        data = await websocket.receive_text()
        data = json.loads(data)
        for next_item in chatbot.stream(data["query"], data["claim"]):
            answer = json.dumps(next_item)
            await websocket.send_text(answer)

# Serve React App
class SPAStaticFiles(StaticFiles):
    async def get_response(self, path: str, scope):
        if len(sys.argv) > 1 and sys.argv[1] == "dev":
            # We are in Dev mode, proxy to the React dev server
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:9000/{path}")
            return Response(response.text, status_code=response.status_code)
        else:
            try:
                return await super().get_response(path, scope)
            except (HTTPException, StarletteHTTPException) as ex:
                if ex.status_code == 404:
                    return await super().get_response("index.html", scope)
                else:
                    raise ex

app.mount("/", SPAStaticFiles(directory="public", html=True), name="spa-static-files")

# Launch the FastAPI server
if __name__ == "__main__":
    port = int(os.getenv('PORT', '8000'))
    run(app, host="0.0.0.0", port=port)
