""" Backend for Parasol Insurance Claims Processing App """
import hashlib
import logging
import os
import sys
import time
from typing import List
import json

import data_classes
import db_utils
import chatbot
import supabase_storage
import httpx
from app_config import LOG_LEVELS, LOGGING_CONFIG
from dotenv import dotenv_values, load_dotenv
from fastapi import FastAPI, File, HTTPException, Request, UploadFile, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from fastapi.staticfiles import StaticFiles
from uvicorn import run
from fastapi import HTTPException
from starlette.exceptions import HTTPException as StarletteHTTPException

# Load local env vars if present
load_dotenv()

# Initialize logger
logger = logging.getLogger("app")

# Get config
config = {
    **dotenv_values(".env"),  # load shared development variables
    **dotenv_values(".env.secret"),  # load sensitive variables
    **os.environ,  # override loaded values with environment variables
}
logger.info(f'Config: INFERENCE_SERVER_URL={config["INFERENCE_SERVER_URL"]}')

# App creation
app = FastAPI()

origins = ["*"]
methods = ["*"]
headers = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=methods,
    allow_headers=headers
)

# Initialize DB
db = db_utils.Database(config, logger)

# Initialize Supabase Storage
storage = supabase_storage.SupabaseStorage(config, logger)

# Initialize Chatbot
chatbot_instance = chatbot.Chatbot(config, logger)

# Status API
@app.get("/health")
async def health():
    """ Basic status """
    return {"message": "Status:OK"}

@app.get("/api/db/tables")
async def db_list_tables():
    """
    List all the available tables in the Database
    """
    tables = db.list_tables()
    return tables

@app.get("/api/db/claims", response_model = List[data_classes.ClaimBaseInfo])
async def db_list_claims():
    """
    List all the claims
    """
    claims = db.list_claims()
    return claims

@app.get("/api/db/claims/{claim_id}", response_model = data_classes.ClaimFullInfo)
async def db_get_claim_info(claim_id):
    """
    Returns the full content of a claim
    """
    claim_info = db.get_claim_info(claim_id)
    return claim_info

@app.post("/api/db/claims", response_model = data_classes.ClaimBaseInfo)
async def db_create_claim(claim: data_classes.ClaimCreationInfo):
    """
    Creates a new claim
    """
    claim_id = db.create_claim(
        claim.subject,
        claim.body,
        client_name=claim.client_name,
        policy_number=claim.policy_number,
        category=claim.category
    )
    claim_info = db.get_claim_base_info(claim_id)
    return claim_info

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    General file upload endpoint for claim documents
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(file.filename.encode()).hexdigest()
    file_key = f"uploads/{timestamp}_{sha256_hash[:8]}_{file.filename}"

    try:
        # Upload the file to Supabase Storage
        storage.upload_file(file_key, file.file.read(), content_type=file.content_type)

        # Get the public URL
        public_url = storage.get_public_url(file_key)

        return {
            "message": f"{file.filename} uploaded successfully",
            "filename": file.filename,
            "key": file_key,
            "url": public_url
        }
    except Exception as e:
        print(f"❌ Upload failed: {e}")
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.post("/api/db/claims/{claim_id}/original_image")
async def db_upload_original_image(claim_id: int, image: UploadFile = File(...)):
    """
    Uploads an original image of a claim
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(image.filename.encode()).hexdigest()
    image_key = f"original_images/{claim_id}_{timestamp}_{sha256_hash[:8]}_{image.filename}"

    # Upload the image to Supabase Storage
    storage.upload_file(image_key, image.file.read(), content_type=image.content_type)
    # Save image info to DB
    db.upload_original_image(claim_id, image.filename, image_key)

    return {"message": f"{image.filename} uploaded as an original image of claim {claim_id}"}

@app.post("/api/db/claims/{claim_id}/processed_image")
async def db_upload_processed_image(claim_id: int, image: UploadFile = File(...)):
    """
    Uploads a processed image of a claim
    """
    # construct new filename with timestamp
    timestamp = int(time.time())
    sha256_hash = hashlib.sha256(image.filename.encode()).hexdigest()
    image_key = f"processed_images/{claim_id}_{timestamp}_{sha256_hash[:8]}_{image.filename}"

    # Upload the image to Supabase Storage
    storage.upload_file(image_key, image.file.read(), content_type=image.content_type)
    # Save image info to DB
    db.upload_processed_image(claim_id, image.filename, image_key)
    
    return {"message": f"{image.filename} uploaded as a processed image of claim {claim_id}"}

@app.post("/api/db/claims/{claim_id}/summary")
async def db_update_claim_summary(claim_id: int, summary: str = None):
    """
    Updates the summary of a claim or generates one using AI
    """
    if summary is None:
        # Generate summary using simplified logic (without Milvus)
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Create a simple summary based on the first 100 characters
        summary = f"Insurance claim summary: {claim_body[:100]}..." if len(claim_body) > 100 else claim_body

    db.update_claim_summary(claim_id, summary)
    return {"message": "Summary generated and uploaded", "summary": summary}

@app.post("/api/db/claims/{claim_id}/time")
async def db_update_claim_time(claim_id: int, time: str = None):
    """
    Updates the time of a claim or extracts it using AI
    """
    if time is None:
        # Extract time using simplified logic (without Milvus)
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Simple time extraction - look for common date patterns
        import re
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',  # MM/DD/YYYY
            r'\d{4}-\d{2}-\d{2}',      # YYYY-MM-DD
            r'\d{1,2}-\d{1,2}-\d{4}'   # MM-DD-YYYY
        ]

        time = "Not specified"
        for pattern in date_patterns:
            match = re.search(pattern, claim_body)
            if match:
                time = match.group()
                break

    db.update_claim_time(claim_id, time)
    return {"message": "Time extracted and uploaded", "time": time}

@app.post("/api/db/claims/{claim_id}/location")
async def db_update_claim_location(claim_id: int, location: str = None):
    """
    Updates the location of a claim or extracts it using AI
    """
    if location is None:
        # Extract location using simplified logic (without Milvus)
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Simple location extraction - look for common location patterns
        import re
        location_patterns = [
            r'\b[A-Z][a-z]+,\s*[A-Z]{2}\b',  # City, ST
            r'\b\d+\s+[A-Z][a-z]+\s+(?:St|Street|Ave|Avenue|Rd|Road|Blvd|Boulevard)\b',  # Street addresses
        ]

        location = "Not specified"
        for pattern in location_patterns:
            match = re.search(pattern, claim_body)
            if match:
                location = match.group()
                break

    db.update_claim_location(claim_id, location)
    return {"message": "Location extracted and uploaded", "location": location}

@app.post("/api/db/claims/{claim_id}/sentiment")
async def db_update_claim_sentiment(claim_id: int, sentiment: str = None):
    """
    Updates the sentiment of a claim or analyzes it using AI
    """
    if sentiment is None:
        # Analyze sentiment using simplified logic (without Milvus)
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"].lower()

        # Simple sentiment analysis based on keywords
        negative_words = ['angry', 'frustrated', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'furious']
        positive_words = ['thank', 'appreciate', 'excellent', 'great', 'wonderful', 'satisfied', 'happy']

        negative_count = sum(1 for word in negative_words if word in claim_body)
        positive_count = sum(1 for word in positive_words if word in claim_body)

        if negative_count > positive_count:
            sentiment = 'negative'
        elif positive_count > negative_count:
            sentiment = 'positive'
        else:
            sentiment = 'neutral'

    db.update_claim_sentiment(claim_id, sentiment)
    return {"message": "Sentiment analyzed and uploaded", "sentiment": sentiment}

@app.post("/api/db/claims/{claim_id}/process")
async def process_claim_with_ai(claim_id: int):
    """
    Comprehensive AI processing of claim including email content and documents
    """
    try:
        # Get claim information
        claim_info = db.get_claim_info(claim_id)
        claim_body = claim_info["body"]

        # Get uploaded documents
        original_images = claim_info.get("original_images", []) or []

        # Generate comprehensive analysis (simplified version without Milvus)
        comprehensive_analysis = f"""
        COMPREHENSIVE CLAIM ANALYSIS

        Email Content Summary:
        {claim_body[:200]}...

        Documents Uploaded: {len(original_images)} files

        Analysis:
        1. Incident Summary: Based on the email content, this appears to be a standard insurance claim.
        2. Key Details: Multiple supporting documents have been uploaded for review.
        3. Status: Ready for manual review by claims adjuster.
        4. Recommended Next Steps: Review uploaded documents and verify claim details.

        Note: This is a simplified analysis. Full AI processing requires additional infrastructure.
        """

        # Trigger individual AI processing endpoints
        await db_update_claim_summary(claim_id)
        await db_update_claim_sentiment(claim_id)
        await db_update_claim_location(claim_id)
        await db_update_claim_time(claim_id)

        return {
            "message": "Comprehensive AI processing completed",
            "analysis": comprehensive_analysis,
            "documents_processed": len(original_images)
        }

    except Exception as e:
        logger.error(f"AI processing failed for claim {claim_id}: {e}")
        raise HTTPException(status_code=500, detail=f"AI processing failed: {str(e)}")

@app.get("/api/images")
async def s3_list_images():
    """
    Returns the list of images
    """
    images = storage.list_files()
    return images

@app.get("/api/images/{image_key:path}")
async def s3_get_image(image_key: str):
    """
    Returns the image with the given key
    """
    # For Supabase Storage, we redirect to the public URL
    public_url = storage.get_public_url(image_key)
    return {"url": public_url}

@app.websocket("/ws/query")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()

    while True:
        data = await websocket.receive_text()
        data = json.loads(data)
        for next_item in chatbot.stream(data["query"], data["claim"]):
            answer = json.dumps(next_item)
            await websocket.send_text(answer)

# Serve React App
class SPAStaticFiles(StaticFiles):
    async def get_response(self, path: str, scope):
        if len(sys.argv) > 1 and sys.argv[1] == "dev":
            # We are in Dev mode, proxy to the React dev server
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:9000/{path}")
            return Response(response.text, status_code=response.status_code)
        else:
            try:
                return await super().get_response(path, scope)
            except (HTTPException, StarletteHTTPException) as ex:
                if ex.status_code == 404:
                    return await super().get_response("index.html", scope)
                else:
                    raise ex

app.mount("/", SPAStaticFiles(directory="public", html=True), name="spa-static-files")

# Launch the FastAPI server
if __name__ == "__main__":
    port = int(os.getenv('PORT', '8000'))
    run(app, host="0.0.0.0", port=port)
