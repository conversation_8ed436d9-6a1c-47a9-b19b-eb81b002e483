[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[[source]]
url = "https://download.pytorch.org/whl/cpu"
verify_ssl = false
name = "pytorch"

[packages]
langchain = "==0.3.1"
langchain-community = "==0.3.1"
langchain-milvus = "==0.1.5"
langchain-huggingface = "==0.1.0"
einops = "==0.8.0"
openai = "==1.47.1"
pymilvus = "==2.4.6"
sentence_transformers = "==3.1.1"
text_generation = "==0.7.0"
boto3 = "==1.34.162"
fastapi = "~=0.115.2"
httpx = "~=0.27.2"
python-dotenv = "~=1.0.1"
psycopg2-binary = "~=2.9.9"
uvicorn = "~=0.31.1"
websockets = "~=13.1"
python-multipart = "0.0.12"
torch = {version = "==2.2.2+cpu", index = "pytorch"}

[dev-packages]
"watchdog[watchmedo]" = "~=5.0.3"
black = "*"

[requires]
python_version = "3.11"
