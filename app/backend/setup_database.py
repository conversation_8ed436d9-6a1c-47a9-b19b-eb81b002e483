#!/usr/bin/env python3
"""
Database setup script for Parasol Insurance application
"""
import psycopg2
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    """Test database connection"""
    try:
        conn = psycopg2.connect(
            host=os.getenv('POSTGRES_HOST', 'db.tlduggpohclrgxbvuzhd.supabase.co'),
            dbname=os.getenv('POSTGRES_DB', 'postgres'),
            user=os.getenv('POSTGRES_USER', 'postgres'),
            password=os.getenv('POSTGRES_PASSWORD'),
            port=int(os.getenv('POSTGRES_PORT', '5432'))
        )
        print('✅ Database connection successful!')
        return conn
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        return None

def create_schema_and_tables(conn):
    """Create the claims schema and tables"""
    cursor = conn.cursor()
    
    try:
        # Create schema
        cursor.execute("CREATE SCHEMA IF NOT EXISTS claims;")
        print('✅ Schema created')
        
        # Create claims table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS claims.claims (
                id SERIAL PRIMARY KEY,
                claim_number TEXT,
                category TEXT,
                policy_number TEXT,
                client_name TEXT,
                subject TEXT,
                body TEXT,
                summary TEXT,
                location TEXT,
                time TEXT,
                sentiment TEXT,
                decision_data TEXT,
                coverage_decision TEXT,
                fault_percentage INTEGER,
                loss_quantum DECIMAL,
                cause_of_loss TEXT
            );
        """)
        print('✅ Claims table created')
        
        # Create original_images table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS claims.original_images (
                id SERIAL PRIMARY KEY,
                image_name TEXT NOT NULL,
                image_key TEXT NOT NULL,
                claim_id INTEGER NOT NULL,
                CONSTRAINT fk_claim_id FOREIGN KEY (claim_id)
                    REFERENCES claims.claims (id) MATCH SIMPLE
                    ON UPDATE CASCADE
                    ON DELETE CASCADE
            );
        """)
        print('✅ Original images table created')
        
        # Create processed_images table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS claims.processed_images (
                id SERIAL PRIMARY KEY,
                image_name TEXT NOT NULL,
                image_key TEXT NOT NULL,
                claim_id INTEGER NOT NULL,
                CONSTRAINT fk_processed_claim_id FOREIGN KEY (claim_id)
                    REFERENCES claims.claims (id) MATCH SIMPLE
                    ON UPDATE CASCADE
                    ON DELETE CASCADE
            );
        """)
        print('✅ Processed images table created')
        
        conn.commit()
        
    except Exception as e:
        print(f'❌ Error creating schema/tables: {e}')
        conn.rollback()
        raise e
    finally:
        cursor.close()

def insert_sample_data(conn):
    """Insert sample claims data"""
    cursor = conn.cursor()
    
    try:
        # Check if data already exists
        cursor.execute("SELECT COUNT(*) FROM claims.claims;")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f'✅ Database already has {count} claims')
            return
        
        # Insert sample claims
        sample_claims = [
            {
                'claim_number': 'CLM202401',
                'category': 'Multiple vehicle',
                'policy_number': 'AC-987654321',
                'client_name': 'Sarah Turner',
                'subject': 'Claim for Recent Car Accident - Policy Number: AC-987654321',
                'body': '''Dear Parasol Insurance,

I hope this email finds you well. My name is Sarah Turner, and I am writing to file a claim for a recent car accident that occurred on January 2nd, 2024, at approximately 3:30 PM. My policy number is AC-987654321.

The accident took place at the intersection of Oak Street and Pine Avenue in downtown Springfield. I was driving eastbound on Oak Street when another vehicle ran a red light and collided with the front passenger side of my car.

The other driver admitted fault at the scene, and the police report (Report #SP240102-001) reflects this. I have attached photos of the damage to my vehicle, which include significant damage to the front bumper, headlight, and passenger door.

I was not seriously injured, but I did visit the emergency room as a precaution and have some minor bruising. I would like to proceed with getting my vehicle repaired and would appreciate guidance on approved repair shops in my area.

Please let me know what additional information or documentation you need to process this claim.

Thank you for your time and assistance.

Best regards,
Sarah Turner
Phone: (*************
Email: <EMAIL>''',
                'summary': None,
                'location': None,
                'time': None,
                'sentiment': None
            },
            {
                'claim_number': 'CLM202402',
                'category': 'Single vehicle',
                'policy_number': 'AC-123456789',
                'client_name': 'Michael Chen',
                'subject': 'Vehicle Damage from Hailstorm - Policy AC-123456789',
                'body': '''Dear Claims Department,

I am writing to report damage to my vehicle from the severe hailstorm that occurred in our area on January 5th, 2024, between 4:00 PM and 5:30 PM.

My car was parked in my driveway at 1234 Maple Street, Springfield, when the storm hit. The hail was approximately golf ball-sized and caused significant damage to the roof, hood, and trunk of my 2022 Honda Accord.

I have taken extensive photos of the damage and can provide them upon request. The vehicle is currently parked in my garage to prevent further damage.

This was an act of nature, and I believe it should be covered under my comprehensive coverage. Please advise on the next steps for getting an estimate and scheduling repairs.

My policy number is AC-123456789, and I can be reached at (*************.

Thank you for your prompt attention to this matter.

Sincerely,
Michael Chen''',
                'summary': None,
                'location': None,
                'time': None,
                'sentiment': None
            },
            {
                'claim_number': 'CLM202403',
                'category': 'Multiple vehicle',
                'policy_number': 'AC-555666777',
                'client_name': 'Jennifer Rodriguez',
                'subject': 'Rear-End Collision Claim - Policy AC-555666777',
                'body': '''To Whom It May Concern,

I am filing a claim for a rear-end collision that occurred on January 8th, 2024, at approximately 8:15 AM during rush hour traffic.

I was stopped at a red light at the intersection of Highway 67 and Commerce Street when the vehicle behind me failed to stop and rear-ended my car. The impact was significant enough to push my vehicle forward several feet.

The other driver was cited for following too closely and failure to maintain control of their vehicle. I have the police report number (HP240108-045) and the other driver's insurance information.

I experienced some neck and back pain immediately after the accident and sought medical attention at Springfield Medical Center. I am currently undergoing physical therapy as recommended by my doctor.

My vehicle sustained damage to the rear bumper, trunk, and rear lights. I have photos and would like to get an estimate for repairs as soon as possible.

Please contact me at your earliest convenience to discuss this claim.

Best regards,
Jennifer Rodriguez
Policy: AC-555666777
Phone: (*************''',
                'summary': None,
                'location': None,
                'time': None,
                'sentiment': None
            }
        ]
        
        for claim in sample_claims:
            cursor.execute("""
                INSERT INTO claims.claims 
                (claim_number, category, policy_number, client_name, subject, body, summary, location, time, sentiment)
                VALUES (%(claim_number)s, %(category)s, %(policy_number)s, %(client_name)s, 
                       %(subject)s, %(body)s, %(summary)s, %(location)s, %(time)s, %(sentiment)s)
            """, claim)
        
        conn.commit()
        print(f'✅ Inserted {len(sample_claims)} sample claims')
        
    except Exception as e:
        print(f'❌ Error inserting sample data: {e}')
        conn.rollback()
        raise e
    finally:
        cursor.close()

def main():
    """Main setup function"""
    print("🚀 Setting up Parasol Insurance database...")
    
    # Test connection
    conn = test_connection()
    if not conn:
        print("❌ Cannot proceed without database connection")
        print("💡 Please check your POSTGRES_PASSWORD in the .env file")
        sys.exit(1)
    
    try:
        # Create schema and tables
        create_schema_and_tables(conn)
        
        # Insert sample data
        insert_sample_data(conn)
        
        print("🎉 Database setup completed successfully!")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        sys.exit(1)
    finally:
        conn.close()

if __name__ == "__main__":
    main()
