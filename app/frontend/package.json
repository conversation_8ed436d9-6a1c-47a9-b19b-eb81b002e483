{"name": "parasol-insurance", "version": "1.0.0", "description": "Backend for the RAG Chatbot Patternfly.", "repository": {"type": "git", "url": "git+https://https://github.com/rh-aiservices-bu/parasol-insurance.git"}, "homepage": "https://github.com/rh-aiservices-bu/parasol-insurance/README.md", "bugs": {"url": "https://github.com/rh-aiservices-bu/parasol-insurance/issues"}, "license": "MIT", "private": true, "scripts": {"prebuild": "npm run type-check && npm run clean", "dr:surge": "node dr-surge.js", "build": "webpack --config ./webpack.prod.js && npm run dr:surge", "start": "sirv dist --cors --single --host --port 8080", "start:dev": "webpack serve --color --progress --config ./webpack.dev.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "eslint": "eslint --ext .tsx,.js ./src/", "lint": "npm run eslint", "format": "prettier --check --write ./src/**/*.{tsx,ts}", "type-check": "tsc --noEmit", "ci-checks": "npm run type-check && npm run lint && npm run test:coverage", "build:bundle-profile": "webpack --config ./webpack.prod.js --profile --json > stats.json", "bundle-profile:analyze": "npm run build:bundle-profile && webpack-bundle-analyzer ./stats.json", "clean": "<PERSON><PERSON><PERSON> dist"}, "devDependencies": {"@redhat-cloud-services/eslint-config-redhat-cloud-services": "^1.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "14.4.3", "@types/jest": "^29.5.3", "@types/react-router-dom": "^5.3.3", "@types/victory": "^33.1.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-plugin-transform-imports": "^2.0.0", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "css-minimizer-webpack-plugin": "^5.0.1", "dotenv-webpack": "^8.0.1", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.3", "imagemin": "^8.0.1", "jest": "^29.6.1", "jest-environment-jsdom": "^29.6.1", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.25", "prettier": "^3.0.0", "prop-types": "^15.8.1", "raw-loader": "^4.0.2", "react-axe": "^3.5.4", "react-docgen-typescript-loader": "^3.7.2", "react-router-dom": "^5.3.4", "regenerator-runtime": "^0.13.11", "rimraf": "^5.0.1", "style-loader": "^3.3.3", "svg-url-loader": "^8.0.0", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "tsconfig-paths-webpack-plugin": "^4.1.0", "tslib": "^2.6.0", "typescript": "^5.1.6", "url-loader": "^4.1.1", "webpack": "^5.88.1", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "webpack-merge": "^5.9.0"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.0", "@patternfly/react-core": "^5.0.0", "@patternfly/react-icons": "^5.0.0", "@patternfly/react-styles": "^5.0.0", "@patternfly/react-table": "^5.1.1", "axios": "^1.6.0", "react": "^18", "react-dom": "^18", "react-image-gallery": "^1.3.0", "sirv-cli": "^2.0.2"}}