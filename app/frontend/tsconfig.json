{"compilerOptions": {"baseUrl": ".", "rootDir": ".", "outDir": "dist", "module": "esnext", "target": "es5", "lib": ["es6", "dom"], "sourceMap": true, "jsx": "react", "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "downlevelIteration": true, "paths": {"@app/*": ["src/app/*"], "@assets/*": ["node_modules/@patternfly/react-core/dist/styles/assets/*"]}, "importHelpers": true, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.jsx", "**/*.js", "**/.eslintrc.js"], "exclude": ["node_modules"]}