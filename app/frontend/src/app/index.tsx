import * as React from 'react';
import '@patternfly/react-core/dist/styles/base.css';
import { BrowserRouter as Router } from 'react-router-dom';
import { AppLayout } from '@app/components/AppLayout/AppLayout';
import { AppRoutes } from '@app/routes';
import '@app/app.css';

const App: React.FunctionComponent = () => {
  console.log('🏁 App component is rendering');
  console.log('🌐 Current URL:', window.location.href);

  // Temporary simple test
  return (
    <div style={{ padding: '20px', backgroundColor: 'red', color: 'white', fontSize: '24px' }}>
      <h1>🚨 REACT APP IS LOADING! 🚨</h1>
      <p>Current URL: {window.location.href}</p>
      <p>If you see this, React is working!</p>
    </div>
  );

  // Original code commented out for testing
  /*
  return (
    <Router>
      <AppLayout>
        <AppRoutes />
      </AppLayout>
    </Router>
  );
  */
};

export default App;
