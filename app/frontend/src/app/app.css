html,
body,
#root {
  height: 100%;
}

.pf-v5-c-content {
  --pf-v5-c-content--small--Color: red; /* changes all <small> color to red */
  --pf-v5-c-content--blockquote--BorderLeftColor: purple; /* changes all <blockquote> left border color to purple */
  --pf-v5-c-content--hr--BackgroundColor: lemonchiffon; /* changes a <hr> color to lemonchiffon */
}

.img-format { /*Default image formatting*/
  margin: 5px; 
  border: 2px solid darkgray;
}

.simple-padding {
  padding: 5px;
}

.display-linebreak {
  white-space: pre-line;
}

.avatar {
  background-color: #ebebeb
}

@import "~react-image-gallery/styles/css/image-gallery.css";

.image-gallery-thumbnail {
  width: 30%;
  height: 30%;
}

.image-gallery-original {
  width: 100%;
  height: 100%;
}

.disabled-link {
  cursor: not-allowed;
  color: grey;
}

.claims-list-filter-select {
  width: 150px;
}

.claims-list-filter-search {
  width: 300px;
}

.width-100 {
  width: 100% !important
} 

.height-100 {
  height: 100% !important
} 

.colored-item-blue {
  color: #056ac7;
}

.padding-bottom-25 {
  padding-bottom: 25px;
}

.padding-top-25 {
  padding-top: 25px;
}

.icon-circle
{
  background: rgb(233, 233, 233); 
  border-radius: 100%;
  padding: 14px;
  width: 15px;
  height: 15px;
  color: rgb(80, 80, 80);
}

.icon-chat-button {
  position: absolute;
  bottom:25px;
  right:12px;
}

.icon-chat
{
  background: #056ac7; 
  border-radius: 100%;
  padding: 10px;
  width: 30px;
  height: 30px;
  color: #ffffff;
  transform: scaleX(-1);
}

.chat-panel
{
  position: absolute;
  bottom:110px;
  right: 25px;
  z-index: 1000;
}

.chat-fadeOut {
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 200ms, opacity 200ms;
}
.chat-fadeIn {
  visibility: visible;
  opacity: 1;
  transition: visibility 0s linear 0s, opacity 500ms;
}

.chat-card {
  border-radius: 10px !important;
  width: 400px;
  height: 550px;
}

.chat-card-header {
  background-color: #056ac7;
  border-top-left-radius: 10px !important;
  border-top-right-radius: 10px !important;
  border-bottom-left-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
  padding: 10px !important;
}

.chat-card-header-title {
  color: #ffffff;
  width: 100%;
  text-align: center;
  padding-top: 0px;
}

.chat-card-body {
  padding: 0px;
}


.chat-bot-answer {
  overflow-wrap: break-word;
  overflow-y: auto;
  height: 100px;
}



.chat-question-text {
  padding-top: 5px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 0px;
  overflow-wrap: break-word;
}

.chat-input-panel {
  padding-top: 1px !important;
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.chat-input-panel-body {
  padding: 5px !important;
}

.chat-disclaimer {
  width: 100%;
  text-align: center;
  font-style: italic;
  font-size: x-small;
  color: #696969;
  padding-bottom: 3px;
  padding-left: 10px;
  padding-right: 10px;
}


.chat-answer-text {
  padding-top: 5px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 5px;
  margin-bottom: 0px !important;
  overflow-wrap: break-word;
}

.chat-source-text {
  font-style: italic;
  padding-top: 5px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 5px;
  margin-bottom: 0px !important;
  overflow-wrap: break-word;
}

.chat-item {
  width: 100%;
  vertical-align: top;
  padding-left: 10px !important;
  padding-right: 10px !important;
  padding-bottom: 10px !important;
  margin-bottom: 0px !important;
}

.grid-item-orb {
  text-align: center;
}

.orb {
  width: 25px;
  height: 25px;
  margin-top: 7px;
}

.user-avatar {
  width: 32px;
  height: 32px;
}