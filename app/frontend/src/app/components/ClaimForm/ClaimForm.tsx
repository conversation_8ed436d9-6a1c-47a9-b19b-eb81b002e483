import config from '@app/config';
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardTitle,
  FileUpload,
  Form,
  FormGroup,
  Page,
  PageSection,
  Text,
  TextArea,
  TextContent,
  TextInput,
  TextVariants,
  Alert,
  Progress,
  Flex,
  FlexItem,
  Label
} from '@patternfly/react-core';
import { CheckCircleIcon, ExclamationCircleIcon } from '@patternfly/react-icons';
import axios from 'axios';
import * as React from 'react';
import { useHistory } from 'react-router-dom';

interface UploadedFile {
  file: File;
  name: string;
  size: string;
  status: 'uploading' | 'success' | 'error';
  url?: string;
}

const ClaimForm: React.FunctionComponent = () => {
  const history = useHistory();
  
  // Form state
  const [emailSubject, setEmailSubject] = React.useState('');
  const [emailBody, setEmailBody] = React.useState('');
  const [senderEmail, setSenderEmail] = React.useState('');
  const [recipientEmail, setRecipientEmail] = React.useState('');
  const [claimCategory, setClaimCategory] = React.useState('');
  const [policyNumber, setPolicyNumber] = React.useState('');
  const [clientName, setClientName] = React.useState('');
  
  // File upload state
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = React.useState(false);
  
  // Form submission state
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitError, setSubmitError] = React.useState('');
  const [submitSuccess, setSubmitSuccess] = React.useState(false);

  // File upload handler
  const handleFileUpload = async (files: File[]) => {
    setIsUploading(true);
    
    for (const file of files) {
      const newFile: UploadedFile = {
        file,
        name: file.name,
        size: formatFileSize(file.size),
        status: 'uploading'
      };
      
      setUploadedFiles(prev => [...prev, newFile]);
      
      try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await axios.post(
          `${config.backend_api_url}/upload`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );
        
        // Update file status to success
        setUploadedFiles(prev => 
          prev.map(f => 
            f.file === file 
              ? { ...f, status: 'success', url: response.data.url }
              : f
          )
        );
      } catch (error) {
        console.error('Upload failed:', error);
        // Update file status to error
        setUploadedFiles(prev => 
          prev.map(f => 
            f.file === file 
              ? { ...f, status: 'error' }
              : f
          )
        );
      }
    }
    
    setIsUploading(false);
  };

  // Remove uploaded file
  const removeFile = (fileToRemove: UploadedFile) => {
    setUploadedFiles(prev => prev.filter(f => f !== fileToRemove));
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Form submission handler
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsSubmitting(true);
    setSubmitError('');
    
    try {
      // Create the claim
      const claimData = {
        subject: emailSubject,
        body: emailBody,
        sender_email: senderEmail,
        recipient_email: recipientEmail,
        category: claimCategory,
        policy_number: policyNumber,
        client_name: clientName
      };
      
      const response = await axios.post(
        `${config.backend_api_url}/db/claims`,
        claimData
      );
      
      const claimId = response.data.id;
      
      // Upload files to the claim if any
      for (const uploadedFile of uploadedFiles.filter(f => f.status === 'success')) {
        try {
          await axios.post(
            `${config.backend_api_url}/db/claims/${claimId}/original_image`,
            { image_url: uploadedFile.url }
          );
        } catch (error) {
          console.error('Failed to associate file with claim:', error);
        }
      }
      
      // Trigger AI processing
      await triggerAIProcessing(claimId);
      
      setSubmitSuccess(true);
      
      // Redirect to the new claim after a short delay
      setTimeout(() => {
        history.push(`/ClaimDetail/${claimId}`);
      }, 2000);
      
    } catch (error) {
      console.error('Claim submission failed:', error);
      setSubmitError('Failed to submit claim. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Trigger AI processing for the claim
  const triggerAIProcessing = async (claimId: number) => {
    try {
      // Trigger all AI analysis endpoints
      await Promise.all([
        axios.post(`${config.backend_api_url}/db/claims/${claimId}/summary`),
        axios.post(`${config.backend_api_url}/db/claims/${claimId}/sentiment`),
        axios.post(`${config.backend_api_url}/db/claims/${claimId}/location`),
        axios.post(`${config.backend_api_url}/db/claims/${claimId}/time`)
      ]);
    } catch (error) {
      console.error('AI processing failed:', error);
      // Don't fail the whole submission if AI processing fails
    }
  };

  if (submitSuccess) {
    return (
      <Page>
        <PageSection>
          <Alert
            variant="success"
            title="Claim submitted successfully!"
            actionClose={<Button variant="plain" onClick={() => setSubmitSuccess(false)} />}
          >
            Your claim has been created and is being processed. You will be redirected to the claim details shortly.
          </Alert>
        </PageSection>
      </Page>
    );
  }

  return (
    <Page>
      <PageSection>
        <TextContent>
          <Text component={TextVariants.h1}>Submit New Claim</Text>
          <Text component={TextVariants.p}>
            Upload documents and provide email content for automated claim processing.
          </Text>
        </TextContent>
      </PageSection>
      
      <PageSection>
        <Form onSubmit={handleSubmit}>
          <Card>
            <CardTitle>Claim Information</CardTitle>
            <CardBody>
              <FormGroup label="Client Name" isRequired fieldId="client-name">
                <TextInput
                  isRequired
                  type="text"
                  id="client-name"
                  value={clientName}
                  onChange={(_event, value) => setClientName(value)}
                  placeholder="Enter client name"
                />
              </FormGroup>
              
              <FormGroup label="Policy Number" isRequired fieldId="policy-number">
                <TextInput
                  isRequired
                  type="text"
                  id="policy-number"
                  value={policyNumber}
                  onChange={(_event, value) => setPolicyNumber(value)}
                  placeholder="Enter policy number"
                />
              </FormGroup>
              
              <FormGroup label="Claim Category" isRequired fieldId="claim-category">
                <TextInput
                  isRequired
                  type="text"
                  id="claim-category"
                  value={claimCategory}
                  onChange={(_event, value) => setClaimCategory(value)}
                  placeholder="e.g., Single vehicle, Multiple vehicle, Theft"
                />
              </FormGroup>
            </CardBody>
          </Card>

          <Card>
            <CardTitle>Email Content</CardTitle>
            <CardBody>
              <FormGroup label="Email Subject" isRequired fieldId="email-subject">
                <TextInput
                  isRequired
                  type="text"
                  id="email-subject"
                  value={emailSubject}
                  onChange={(_event, value) => setEmailSubject(value)}
                  placeholder="Enter email subject"
                />
              </FormGroup>
              
              <FormGroup label="Email Body" isRequired fieldId="email-body">
                <TextArea
                  isRequired
                  id="email-body"
                  value={emailBody}
                  onChange={(_event, value) => setEmailBody(value)}
                  placeholder="Enter the full email content describing the incident..."
                  rows={8}
                />
              </FormGroup>
              
              <FormGroup label="Sender Email" fieldId="sender-email">
                <TextInput
                  type="email"
                  id="sender-email"
                  value={senderEmail}
                  onChange={(_event, value) => setSenderEmail(value)}
                  placeholder="<EMAIL>"
                />
              </FormGroup>
              
              <FormGroup label="Recipient Email" fieldId="recipient-email">
                <TextInput
                  type="email"
                  id="recipient-email"
                  value={recipientEmail}
                  onChange={(_event, value) => setRecipientEmail(value)}
                  placeholder="<EMAIL>"
                />
              </FormGroup>
            </CardBody>
          </Card>

          <Card>
            <CardTitle>Document Upload</CardTitle>
            <CardBody>
              <FormGroup label="Upload Documents" fieldId="file-upload">
                <FileUpload
                  id="file-upload"
                  type="dataURL"
                  multiple
                  allowEditingUploadedText={false}
                  onFileInputChange={(_event, files) => {
                    if (files) {
                      const fileArray = files instanceof FileList ? Array.from(files) : [files];
                      handleFileUpload(fileArray);
                    }
                  }}
                  browseButtonText="Choose files"
                  clearButtonText="Clear"
                />
              </FormGroup>
              
              {uploadedFiles.length > 0 && (
                <div style={{ marginTop: '1rem' }}>
                  <Text component={TextVariants.h4}>Uploaded Files:</Text>
                  {uploadedFiles.map((file, index) => (
                    <Flex key={index} style={{ marginTop: '0.5rem', alignItems: 'center' }}>
                      <FlexItem>
                        {file.status === 'success' && <CheckCircleIcon color="green" />}
                        {file.status === 'error' && <ExclamationCircleIcon color="red" />}
                        {file.status === 'uploading' && <Progress size="sm" value={50} />}
                      </FlexItem>
                      <FlexItem>
                        <Text>{file.name} ({file.size})</Text>
                      </FlexItem>
                      <FlexItem>
                        <Label color={file.status === 'success' ? 'green' : file.status === 'error' ? 'red' : 'blue'}>
                          {file.status}
                        </Label>
                      </FlexItem>
                      <FlexItem>
                        <Button variant="link" onClick={() => removeFile(file)}>Remove</Button>
                      </FlexItem>
                    </Flex>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>

          {submitError && (
            <Alert variant="danger" title="Submission Error">
              {submitError}
            </Alert>
          )}

          <Flex>
            <FlexItem>
              <Button
                type="submit"
                variant="primary"
                isLoading={isSubmitting}
                isDisabled={isSubmitting || isUploading || !emailSubject || !emailBody || !clientName || !policyNumber}
              >
                {isSubmitting ? 'Submitting Claim...' : 'Submit Claim'}
              </Button>
            </FlexItem>
            <FlexItem>
              <Button variant="secondary" onClick={() => history.push('/ClaimsList')}>
                Cancel
              </Button>
            </FlexItem>
          </Flex>
        </Form>
      </PageSection>
    </Page>
  );
};

export { ClaimForm };
