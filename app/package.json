{"name": "parasol-insurance", "version": "1.0.0", "description": "Parasol Insurance Claim Processing.", "author": "", "license": "MIT", "private": true, "repository": {"type": "git", "url": "git+https://https://github.com/rh-aiservices-bu/parasol-insurance.git"}, "homepage": "https://github.com/rh-aiservices-bu/parasol-insurance/README.md", "bugs": {"url": "https://github.com/rh-aiservices-bu/parasol-insurance/issues"}, "engines": {"node": ">=18.0.0"}, "scripts": {"build": "run-p -l build:*", "build:backend": "cd ./backend && export PATH=$PATH:/opt/app-root/src/.local/bin && python3 -m ensurepip --upgrade && pip3 install -r requirements.txt", "build:frontend": "cd ./frontend && npm run build && cp -r ./dist/* ../backend/public", "dev": "run-p -l dev:*", "dev:backend": "cd ./backend && source ./.venv/bin/activate && nodemon app.py dev", "dev:frontend": "cd ./frontend && npm run start:dev", "format": "prettier --write \"backend/**/*.ts\" \"frontend/**/*.ts\" \"frontend/**/*.tsx\"", "make": "make", "make:build": "make build", "make:deploy": "make deploy", "make:login": "make login", "make:undeploy": "make undeploy", "make:push": "make push", "postinstall": "run-p postinstall:*", "postinstall:backend": "echo \"backend venv needs to be activated\"", "postinstall:frontend": "cd ./frontend && npm install", "start": "run-p start:*", "start:backend": "cd ./backend && python3 app.py", "start:frontend": "echo \"...available at ./frontend/public\"", "test": "run-s test:backend test:frontend", "test:backend": "cd ./backend && npm run test", "test:frontend": "cd ./frontend && npm run test", "test:e2e": "cd ./frontend && npm run test:e2e", "test:accessibility": "cd ./frontend && npm run test:accessibility", "test:integration": "cd ./frontend && npm run test:integration", "test:unit": "cd ./frontend && npm run test:unit", "test:fix": "run-s test:fix-backend test:fix-frontend", "test:fix-backend": "cd ./backend && npm run test:fix", "test:fix-frontend": "cd ./frontend && npm run test:fix"}, "dependencies": {"dotenv": "^8.2.0", "dotenv-expand": "^5.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.2.1"}}