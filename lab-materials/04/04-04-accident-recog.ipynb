{"cells": [{"cell_type": "markdown", "id": "70bedcd9-6e54-4fe7-951f-807c6ab2f0e9", "metadata": {}, "source": ["# Test the retrained YOLO model\n", "\n", "Now that we have retrained our model we can use it against our 'test' images."]}, {"cell_type": "code", "execution_count": null, "id": "cb59c2a5-7de8-48b2-8f32-a83b4b42a7ab", "metadata": {}, "outputs": [], "source": ["# If you did not use the Workbench image designed for this Lab, you can uncomment and run the following line to install the required packages.\n", "# !pip install --no-cache-dir --no-dependencies -r requirements.txt\n", "\n", "from ultralytics import YOLO\n", "import numpy as np\n", "import cv2\n", "from matplotlib import pyplot as plt\n", "\n", "import remote_infer"]}, {"cell_type": "markdown", "id": "5c5cc11d-54eb-4f95-a92a-4b9f6cf1fac6", "metadata": {}, "source": ["In the last notebook we saw how to re-trained the model. As this is a time-consuming tasks that would require GPUs to be efficient, we cannot really do it during the workshop.\n", "\n", "So for your convenience, an already re-trained model, exported in the ONNX format, is available."]}, {"cell_type": "code", "execution_count": null, "id": "1a927c99-c8d0-44d2-94ce-be033b8ca0de", "metadata": {}, "outputs": [], "source": ["# Get the newly trained model.\n", "\n", "model = YOLO(\"https://rhods-public.s3.amazonaws.com/demo-models/ic-models/accident/accident_detect.onnx\", task=\"detect\")"]}, {"cell_type": "code", "execution_count": null, "id": "bdab1fa5-1d47-4060-b32a-e53f52a7a993", "metadata": {}, "outputs": [], "source": ["# Test the model against a car accident image\n", "image_path = \"images/carImage3.jpg\"  # We know that Image3 represents a severe accident with over 86% certainty.\n", "\n", "_, scale, original_image = remote_infer.preprocess(image_path)\n", "\n", "original_image: np.ndarray = cv2.imread(image_path)\n", "blob = cv2.dnn.blobFromImage(original_image, size=(640, 640), swapRB=False)\n", "blob = np.ascontiguousarray(blob[0].transpose((1, 2, 0)))\n", "results = model.predict(blob)"]}, {"cell_type": "code", "execution_count": null, "id": "04757541-e559-4a12-9f99-98ca183452d7", "metadata": {"tags": []}, "outputs": [], "source": ["# Extract all the information from the result (type, bounding box, probability)\n", "\n", "detections = []\n", "result = results[0]\n", "for box in result.boxes:\n", "    class_id = int(box.cls.item())\n", "    score = box.conf.item()\n", "    unscaled_cords = box.xyxy.squeeze().tolist()\n", "    cords = [round(unscaled_cords[0] * scale[1]), round(unscaled_cords[1] * scale[0]), round(unscaled_cords[2] * scale[1]), round(unscaled_cords[3] * scale[0])]\n", "    detection = {\n", "        'class_id': class_id,\n", "        'class_name': result.names[class_id],\n", "        'confidence': score,\n", "        'box': cords,\n", "        'scale': scale}\n", "    detections.append(detection)\n", "    print(detection)\n", "    remote_infer.draw_bounding_box(original_image, class_id, score, cords[0], cords[1], cords[2], cords[3])"]}, {"cell_type": "code", "execution_count": null, "id": "cefc9691-0cd5-446f-b4b6-fc02112ef2fa", "metadata": {"tags": []}, "outputs": [], "source": ["# On the image, draw the box, the name of the class, and the probability (how much the model is sure about the detection).\n", "\n", "img = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)\n", "fig = plt.gcf()\n", "fig.set_size_inches(6, 3)\n", "plt.axis('off')\n", "plt.imshow(img)"]}, {"cell_type": "markdown", "id": "1784c664-01ea-4787-acd7-631854caa9fd", "metadata": {}, "source": ["In examining 'carImage3.jpg' the re-trainined YOLO model correctly predicts a 'car accident' with 91% confidence.  A box is then drawn around the car accident and labelled with `severe 0.91`.\n", "\n", "Now that we have a model which can detect accident severity, let's create a predict function and serve our  model with ModelMesh.\n", "\n", "**Go back to the Instructions of the Lab, to learn how to do that.**\n", "\n", "**DO NOT open notebook `04-05-model-serving.ipynb` yet**."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}