{"cells": [{"cell_type": "markdown", "id": "917ad428-9d47-46eb-aa7b-b0cbd485f2ce", "metadata": {}, "source": ["# Detect a car and place bounding boxes around it\n", "\n", "In this notebook we will learn how to draw boxes around the cars that have been detected by the Yolo model."]}, {"cell_type": "code", "execution_count": null, "id": "03c4c9cc-1e3a-43a4-92cf-44af26573030", "metadata": {"tags": []}, "outputs": [], "source": ["# If you did not use the Workbench image designed for this Lab, you can uncomment and run the following line to install the required packages.\n", "# !pip install --no-cache-dir --no-dependencies -r requirements.txt\n", "\n", "from ultralytics import YOLO\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": null, "id": "0aa262cd-c629-45a5-a718-de61a585b710", "metadata": {"tags": []}, "outputs": [], "source": ["# We are going to use the YOLOv8m model for object detection\n", "\n", "model = YOLO(\"yolov8m.pt\")"]}, {"cell_type": "code", "execution_count": null, "id": "576ca31b-b8f1-45a9-ae12-2f5420e5fbbe", "metadata": {"tags": []}, "outputs": [], "source": ["# Obtain the results for the model prediction on a test images\n", "\n", "img = \"images/carImage0.jpg\"\n", "results = model.predict(img)"]}, {"cell_type": "code", "execution_count": null, "id": "f7516db3-b51c-4699-b63b-4c1d9c307a73", "metadata": {"tags": []}, "outputs": [], "source": ["# With YOLO you can submit an array of images, not just one, and you get an array of results.\n", "# As we submitted only one image, we need to retrieve only the first (and only) element from our array of results.\n", "\n", "result = results[0]"]}, {"cell_type": "code", "execution_count": null, "id": "720e6638-cced-440c-afb1-6270b74f3bb6", "metadata": {"tags": []}, "outputs": [], "source": ["# Detect how many boxes were found\n", "\n", "len(result.boxes)"]}, {"cell_type": "code", "execution_count": null, "id": "243d234e-8c90-4c0e-a78d-d304faabee71", "metadata": {"tags": []}, "outputs": [], "source": ["# Analyze the box\n", "\n", "box = result.boxes[0]\n", "print(\"Object type:\", box.cls)\n", "print(\"Coordinates:\", box.xyxy)\n", "print(\"Probability:\", box.conf)"]}, {"cell_type": "code", "execution_count": null, "id": "6922f2d3-0849-4567-9c5d-2232b19eeae4", "metadata": {"tags": []}, "outputs": [], "source": ["# Unpack the actual values from the Tensor\n", "\n", "cords = box.xyxy[0].tolist()\n", "class_id = box.cls[0].item()\n", "conf = box.conf[0].item()\n", "print(\"Object type:\", class_id)\n", "print(\"Coordinates:\", cords)\n", "print(\"Probability:\", conf)"]}, {"cell_type": "code", "execution_count": null, "id": "9bfe8462-eec5-4769-ba8a-cf0fec8dfea2", "metadata": {"tags": []}, "outputs": [], "source": ["# COCO is the dataset on which the YOLO model has been trained. The objects it has been trained to detect are organized in classes. This is the info we got with the \"Object type\" field.\n", "# The YOLOv8 result object also contains the 'names' property for these classes.\n", "\n", "print(result.names)"]}, {"cell_type": "markdown", "id": "cb88c825-0144-49e7-9434-02ffdc2212c6", "metadata": {}, "source": ["\n", "We see that class number '2' correspond to a 'car' object.  So the bounding box in our result corresponds to a detected 'car'.\n", "Let's draw the box on the image!\n"]}, {"cell_type": "code", "execution_count": null, "id": "f5c4d962-1f36-40ac-b399-e819023a4a05", "metadata": {"tags": []}, "outputs": [], "source": ["# First, put the coordinates in a list, and round them.\n", "# Then get the name of the detected object class by ID using the result.names dictionary.\n", "\n", "cords = box.xyxy[0].tolist()\n", "cords = [round(x) for x in cords]\n", "class_id = result.names[box.cls[0].item()]\n", "conf = round(box.conf[0].item(), 2)\n", "print(\"Object type:\", class_id)\n", "print(\"Coordinates:\", cords)\n", "print(\"Probability:\", conf)"]}, {"cell_type": "code", "execution_count": null, "id": "76cc667b-779d-4bbd-a2d4-3fb8b7c4ff22", "metadata": {"tags": []}, "outputs": [], "source": ["# Let's loop over all the boxes to extract the information.\n", "\n", "for box in result.boxes:\n", "    class_id = result.names[box.cls[0].item()]\n", "    cords = box.xyxy[0].tolist()\n", "    cords = [round(x) for x in cords]\n", "    conf = round(box.conf[0].item(), 2)\n", "    print(\"Object type:\", class_id)\n", "    print(\"Coordinates:\", cords)\n", "    print(\"Probability:\", conf)\n", "    print(\"---\")"]}, {"cell_type": "code", "execution_count": null, "id": "79374798-8dc6-4235-9d4e-dc1c0e397bf5", "metadata": {"tags": []}, "outputs": [], "source": ["# On the image, draw the box, the name of the class, and the probability (how much the model is sure about the detection).\n", "\n", "Image.fromarray(result.plot()[:, :, ::-1])"]}, {"cell_type": "markdown", "id": "7b83cb59-33b2-42dc-b001-b7e1af41d064", "metadata": {}, "source": ["Now, let's go back to the multiple car image (carImage4.jpg) that we tested in the last notebook, and determine if yolo is indeed able to identify all the 'cars' in the image."]}, {"cell_type": "code", "execution_count": null, "id": "b946358d-727a-4d08-9f9b-5c7cd9ee9c29", "metadata": {"tags": []}, "outputs": [], "source": ["# This is the same code we used in the previous cells, but in one pass\n", "\n", "results = model.predict(\"images/carImage4.jpg\")\n", "\n", "result = results[0]\n", "\n", "for box in result.boxes:\n", "    class_id = result.names[box.cls[0].item()]\n", "    cords = box.xyxy[0].tolist()\n", "    cords = [round(x) for x in cords]\n", "    conf = round(box.conf[0].item(), 2)\n", "    print(\"Object type:\", class_id)\n", "    print(\"Coordinates:\", cords)\n", "    print(\"Probability:\", conf)\n", "    print(\"---\")\n", "\n", "Image.fromarray(result.plot()[:, :, ::-1])"]}, {"cell_type": "markdown", "id": "e285a42c-1763-427b-9806-943bf94aa219", "metadata": {}, "source": ["We can see that the YOLO model did miss some cars that are in the 'far back' of the image.  But overall, the model did a great job of identifying multiple cars in this image.  And more importantly we can see that the identified cars are surrounded by a 'bounding box'!\n", "\n", "Now that we are able to place bounding boxes around the car(s) recognized by the yolo model, we can re-train our YOLO model to identify a car 'crash'. \n", "\n", "**Please open the notebook `04-03-model-retraining.ipynb`.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}