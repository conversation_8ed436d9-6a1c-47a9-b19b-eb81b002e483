{"cells": [{"cell_type": "markdown", "id": "508be7fd-7c1d-46b1-945d-d0da8ed19c82", "metadata": {}, "source": ["# Overview of approach used for detecting car crashes\n", "\n", "We have been tasked with developing an application which can identify if an insurance claim contains a car accident displayed in a static image. If an accident is detected, the claim is forwarded to the correct claims processing department.  Let's start by learning how to use a YOLO model.  \n", "\n", "Note:  YOL<PERSON> stands for <I> 'You Only Look Once'</I>, a nickname for the algorithm used in the model.\n", "\n", "In this notebook we use a pre-trained machine learning model, YOLOv8m and explore how it works on static images. If it can detect a car then we can try to retrain the model to detect car accidents.  \n", "\n", "To begin, we will import the `ultralytics` package used to work with YOLO models, as well as other python packages:"]}, {"cell_type": "code", "execution_count": null, "id": "952e0a5e-e038-46bc-b7a2-16ca7ec64af5", "metadata": {"tags": []}, "outputs": [], "source": ["# If you did not use the Workbench image designed for this Lab, you can uncomment and run the following line to install the required packages.\n", "# !pip install --no-cache-dir --no-dependencies -r requirements.txt\n", "\n", "from ultralytics import YOLO\n", "from PIL import Image\n", "from IPython.display import display"]}, {"cell_type": "code", "execution_count": null, "id": "ff63f26e-1a19-4409-ad99-d0abb45d74aa", "metadata": {"tags": []}, "outputs": [], "source": ["# We are going to use the YOLOv8m model for object detection\n", "model = YOLO('yolov8m.pt')"]}, {"cell_type": "code", "execution_count": null, "id": "568460ea-b6b2-48b6-87fb-2a7d2f8091e0", "metadata": {"tags": []}, "outputs": [], "source": ["# Display the image we wish to test\n", "Image.open(\"images/carImage0.jpg\")"]}, {"cell_type": "code", "execution_count": null, "id": "c4c129c0-e309-45e3-b094-ee4ee999dd7d", "metadata": {"tags": []}, "outputs": [], "source": ["# Test that the YOLO model can identify a car object in the given photo\n", "results = model.predict(\"images/carImage0.jpg\")"]}, {"cell_type": "markdown", "id": "86e1bd1b-123c-4fac-ab18-da40323cc97e", "metadata": {}, "source": ["We see that YOLO has determined that there is '1 car' in the provided photo.  This is great news for us.  We can test out a few more photos to make certain YOLO is detecting car objects."]}, {"cell_type": "code", "execution_count": null, "id": "c00d4d9a-7b03-479a-9995-f0a0cddbf1e1", "metadata": {"tags": []}, "outputs": [], "source": ["# List all the images in our <images> folder. We can then pick one of those images for our tests.\n", "!ls -al images"]}, {"cell_type": "code", "execution_count": null, "id": "1c920270-62a0-41be-a8e3-0ccf57771fcc", "metadata": {"tags": []}, "outputs": [], "source": ["# Choose a car image,  eg <carImage4.jpg>, and see if yolo can determine if the image contains an automobile.\n", "img = Image.open('images/carImage4.jpg')\n", "img_resized = img.resize((500, 300))\n", "display(img_resized)"]}, {"cell_type": "code", "execution_count": null, "id": "26771df8-1e76-4aee-893d-1200b9871fff", "metadata": {"tags": []}, "outputs": [], "source": ["# Obtain the results for the model prediction\n", "results = model.predict(img)"]}, {"cell_type": "markdown", "id": "24a0c24c-9847-4ab0-a242-ca236fffda3e", "metadata": {}, "source": ["If we look at the results for <carImage4.jpg>, we see that yolo has determined that there are 17 cars and 1 truck.  This result is interesting.  We have confirmed that the yolo model can identify multiple 'cars' in an image that contains multiple vehicles.\n", "\n", "However, with so many vehicles displayed in the image, it may now be difficult for us, 'the humans', to accurately count all of the vehicles!\n", "\n", "Therefore let's write some code that will draw boxes around the cars that the yolo model identifies within an image.\n", "\n", "**Please open the notebook `04-02-car-recog.ipynb`**."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}