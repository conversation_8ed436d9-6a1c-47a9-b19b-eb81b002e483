{"cells": [{"cell_type": "markdown", "id": "f8cbdac0-3371-48af-aaa9-c40bbbcb62ff", "metadata": {}, "source": ["# Retrain the YOLO model\n", "\n", "To retrain the YOLO model we need a prepared dataset of car images with moderate and severe accident labels.  We have such a dataset (obtained from RoboFlow) that already contains annotated images, splitted into training and validation datasets.  We will use these training/validation sets to retrain our current YOLO model. A few info on the data structure needed for training:\n", "\n", "1. The encode classes of objects we want to teach our model to detect is 0-`moderate` and 1-`severe`.\n", "2. The dataset will be in its own folder, with 2 subfolders in it: `train` and `valid`.  Within each subfolder there will be 2 subfolders: `images` and `labels`.\n", "3. Each image has a corresponding annotation text file in the `labels` subfolder. The annotation text files have the same names as the image files.\n", "4. A dataset descriptor YAML file (data.yaml) points to the datasets and describes the object classes in them. This YAML file is passed to the `train` method of the model to start the training process.\n", "\n", "Let's get started!"]}, {"cell_type": "code", "execution_count": null, "id": "602e0c59-42b1-4de4-9de2-d0875cf597c8", "metadata": {"tags": []}, "outputs": [], "source": ["# If you did not use the Workbench image designed for this Lab, you can uncomment and run the following line to install the required packages.\n", "# !pip install --no-cache-dir --no-dependencies -r requirements.txt\n", "\n", "import os\n", "import requests\n", "import zipfile\n", "from tqdm.notebook import tqdm\n", "from ultralytics import YOLO"]}, {"cell_type": "markdown", "id": "cc755b4b-db03-4b43-9da9-80ed8298417b", "metadata": {}, "source": ["Next let's load a YOLO model 'yolo8m.pt'"]}, {"cell_type": "code", "execution_count": null, "id": "6e595eb2-0e11-4dce-9d5c-2a2025ddcd6d", "metadata": {"tags": []}, "outputs": [], "source": ["# Load model\n", "model = YOLO('yolov8m.pt')  # load a pretrained model (recommended for training)"]}, {"cell_type": "markdown", "id": "37d43616-2d25-419b-bd30-47a5aafb0d33", "metadata": {"tags": []}, "source": ["## Get the training data\n", "\n", "We have provided the following 2 training data sets, available as zip files:  \n", "1) `accident-full.zip`   - to be used to fully re-train the model.\n", "2) `accident-sample.zip` - to be used to partially re-train the model when we don't have the time to fully re-train the model.\n", "\n", "During the workshop we will only use the sample dataset."]}, {"cell_type": "code", "execution_count": null, "id": "2657ceae-db2d-4d65-ae1c-e807254ee71b", "metadata": {"tags": []}, "outputs": [], "source": ["def retrieve_dataset(dataset_type):\n", "    # Function to retrieve a specific dataset\n", "    # Check if the directory exists, if not, create it\n", "    if not os.path.exists(\"./datasets/\"):\n", "        os.makedirs(\"./datasets/\")\n", "\n", "    URL = f\"https://rhods-public.s3.amazonaws.com/sample-data/accident-data/accident-{dataset_type}.zip\"\n", "\n", "    # Check if the file exists, if not, download and unzip it\n", "    if not os.path.exists(f\"./datasets/accident-{dataset_type}.zip\"):\n", "        print(\"Downloading file...\")\n", "        response = requests.get(URL, stream=True)\n", "        total_size = int(response.headers.get('content-length', 0))\n", "        block_size = 1024\n", "        t = tqdm(total=total_size, unit='iB', unit_scale=True)\n", "        with open(f'./datasets/accident-{dataset_type}.zip', 'wb') as f:\n", "            for data in response.iter_content(block_size):\n", "                t.update(len(data))\n", "                f.write(data)\n", "        t.close()\n", "    if os.path.exists(f\"./datasets/accident-{dataset_type}.zip\"):\n", "        print(\"Unzipping file...\")\n", "        with zipfile.ZipFile(f'./datasets/accident-{dataset_type}.zip', 'r') as zip_ref:\n", "            zip_ref.extractall(path='./datasets/')\n", "    print(\"Done!\")\n", "\n", "\n", "dataset_type = 'sample'\n", "# dataset_type = 'full' # Use this line instead if you want to retrieve the full dataset\n", "retrieve_dataset(dataset_type)"]}, {"cell_type": "markdown", "id": "93e7d5b4-4c29-4eff-b1ba-a9dddf382173", "metadata": {"tags": []}, "source": ["## Re-training our YOLO model\n", "\n", "Let's start by understanding what an 'epoch' is.  Machine learning models are trained with specific datasets passed through an algorithm. Each time the full dataset passes through the algorithm, it is said to have completed an **epoch**. Each **epoch** will further refine the training of the model.\n", "\n", "In the training code below we will set various parameters:  \n", "**results = model.train(data='./datasets/accident-sample/data.yaml', epochs=1, imgsz=640, batch=2)**\n", "\n", "- epochs: as we want to only demo the process, we will use only 1 epoch.\n", "- imgsz: this is the size of the images the model has to be fed with.\n", "- batch: this is the number of images that the algorithm will process simultaneously. The more the better results, but the more memory it consumes. As we are on constrained resources in this workshop, and this is only a training example, we keep it intentionally low at 2.\n", "\n", "In your training run, each **epoch<** will show a summary for both the training and validation phases: lines 1 and 2 show results of the training phase and lines 3 and 4 show the results of the validation phase for each epoch.  \n", "\n", "Execute the following cell to start re-training the model!"]}, {"cell_type": "code", "execution_count": null, "id": "22954895-0c26-4ae0-93c1-259efd18e47e", "metadata": {"tags": []}, "outputs": [], "source": ["# Train the model\n", "\n", "results = model.train(data='./datasets/accident-sample/data.yaml', epochs=1, imgsz=640, batch=2)"]}, {"cell_type": "markdown", "id": "45050c91-fb6c-4f14-a4d1-e72d3a40538f", "metadata": {}, "source": ["**Note**: after a standard re-training, if we are happy with the results, we could export our model to the ONNX format. \n", "(you would replace train<PERSON> by the traininig session you want to use in the following command)\n", "\n", "`ObjDetOXModel = YOLO(\"runs/detect/trainX/weights/best.pt\").export(format=\"onnx\")`"]}, {"cell_type": "markdown", "id": "cd0f7412-0b9b-46d1-b2f6-ded479b20cb7", "metadata": {}, "source": ["## Interpreting our Training Results\n", "\n", "A full description of the process and the results for a training against the **full dataset** is available in the Lab instructions.\n", "\n", "Now that we have retrained our model let's test it against images with car accidents!\n", "\n", "**Please open the notebook `04-04-accident-recog.ipynb`**."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}