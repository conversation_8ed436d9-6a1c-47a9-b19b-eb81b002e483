{"cells": [{"cell_type": "markdown", "id": "e4f4dada-51a8-4f06-9618-0f2deda72bee", "metadata": {"tags": []}, "source": ["# Model Serving \n", "\n", "In notebook '04-04-accident-recognition' we were able to use the retrained model to predict a 'severe' or 'moderate' car accident within an image.  \n", "\n", "Now we will determine if we can query the model directly from the model server we have created.  This will be done through an API call.\n", "\n", "**Important**: First, enter the inference endpoint URL you got after deploying the model server."]}, {"cell_type": "code", "execution_count": null, "id": "e38338ba-d858-4096-92a1-284d35679005", "metadata": {"tags": []}, "outputs": [], "source": ["# Normally, this should be\n", "# RestURL = 'http://modelmesh-serving.userX:8008' , with userX being replaced by the user you have been assigned.\n", "# CHANGE the value below, or nothing will work!\n", "RestURL = 'http://modelmesh-serving.user1:8008'"]}, {"cell_type": "code", "execution_count": null, "id": "03c4c9cc-1e3a-43a4-92cf-44af26573030", "metadata": {"tags": []}, "outputs": [], "source": ["# If you did not use the Workbench image designed for this Lab, you can uncomment and run the following line to install the required packages.\n", "# !pip install --no-cache-dir --no-dependencies -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "930247ab-2e10-4db4-888c-517ca3a4cec8", "metadata": {"tags": []}, "outputs": [], "source": ["import cv2\n", "from matplotlib import pyplot as plt\n", "from PIL import Image\n", "## note that to keep the notebook readable, a lot of code was moved into the file remote_infer.py.\n", "## if you are curious, open it to see the various functions required around this prediction\n", "from remote_infer import process_image"]}, {"cell_type": "markdown", "id": "c5e84546-979c-4dc2-9fc7-21d997001beb", "metadata": {}, "source": ["## We will define the inference URL, the model name, the YAML file with your classes"]}, {"cell_type": "code", "execution_count": null, "id": "329ac64a-76a4-4630-8da3-0174fbdf54cd", "metadata": {"tags": []}, "outputs": [], "source": ["infer_url = f'{RestURL}/v2/models/my-first-model/infer'"]}, {"cell_type": "markdown", "id": "e74d67e4-1933-4957-80fd-c4b143d14279", "metadata": {"tags": []}, "source": ["## Now we set the parameters for the inference"]}, {"cell_type": "code", "execution_count": null, "id": "31725659-710d-4fdc-b195-4d56b299f419", "metadata": {"tags": []}, "outputs": [], "source": ["# 1. The image you want to analyze\n", "image_path = 'images/carImage3.jpg'  # You can replace this with an image you upload"]}, {"cell_type": "markdown", "id": "326d21db-0f7b-4f89-86bd-84f4c04b7c44", "metadata": {}, "source": ["## Launch the inference and show the result"]}, {"cell_type": "code", "execution_count": null, "id": "c15e0ccd-5c65-4dc7-80bc-2e583223ac59", "metadata": {"tags": []}, "outputs": [], "source": ["# Launch the inference by calling the remote model server\n", "img = process_image(image_path, infer_url)\n", "\n", "# Display the result\n", "img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "fig = plt.gcf()\n", "fig.set_size_inches(6, 3)\n", "plt.axis('off')\n", "plt.imshow(img)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}