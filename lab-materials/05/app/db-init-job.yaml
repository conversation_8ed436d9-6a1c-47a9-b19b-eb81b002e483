apiVersion: batch/v1
kind: Job
metadata:
  name: db-init-job
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  template:
    spec:
      initContainers:
      - name: wait-for-db
        image: busybox:1.28
        command: ['sh', '-c', 'until nc -z -v -w30 $POSTGRESQL_DATABASE 5432; do echo "Waiting for database connection..."; sleep 2; done;']
        env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POSTGRESQL_DATABASE
          value: claimdb.$(NAMESPACE).svc.cluster.local
      containers:
      - name: postgresql
        image: registry.redhat.io/rhel9/postgresql-13:latest
        env:
          - name: NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
          - name: POSTGRESQL_DATABASE
            valueFrom:
              secretKeyRef:
                name: claimdb
                key: database-name
          - name: POSTGRESQL_USER
            valueFrom:
              secretKeyRef:
                name: claimdb
                key: database-user
          - name: PGPASSWORD
            valueFrom:
              secretKeyRef:
                name: claimdb
                key: database-password
          - name: POSTGRESQL_DATABASE_HOST
            value: claimdb.$(NAMESPACE).svc.cluster.local
        command: ["/bin/bash", "-c"]
        args:
        - |
          echo "Running SQL script"
          psql -h $POSTGRESQL_DATABASE_HOST -p 5432 -U $POSTGRESQL_USER -d $POSTGRESQL_DATABASE -f /sql-script/script.sql
        volumeMounts:
        - name: sql-script-volume
          mountPath: /sql-script
      restartPolicy: Never
      volumes:
      - name: sql-script-volume
        configMap:
          name: sql-script-configmap
  backoffLimit: 4