---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ic-app
  annotations:
    argocd.argoproj.io/sync-wave: "2"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ic-app
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: ic-app
        deployment: ic-app
    spec:
      containers:
        - name: insurance-claim-app
          image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-app:2.2.1
          ports:
            - containerPort: 5000
              protocol: TCP
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INFERENCE_SERVER_URL
              value: http://granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local:8080/v1
            - name: MODEL_NAME
              value: 'granite-3-1-8b-instruct'
            - name: MAX_TOKENS
              value: '512'
            - name: TOP_P
              value: '0.95'
            - name: TEMPERATURE
              value: '0.01'
            - name: PRESENCE_PENALTY
              value: '1.03'
            - name: POSTGRES_HOST
              value: claimdb.$(NAMESPACE).svc.cluster.local
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-name
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-user
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-password
            - name: POSTGRES_PORT
              value: '5432'
            - name: S3_ENDPOINT_URL
              value: http://minio.ic-shared-minio.svc.cluster.local:9000
            - name: IMAGES_BUCKET
              value: $(NAMESPACE)
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: secret-minio
                  key: aws_access_key_id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-minio
                  key: aws_secret_access_key
            - name: MILVUS_HOST
              value: vectordb-milvus.ic-shared-milvus.svc.cluster.local
            - name: MILVUS_PORT
              value: '19530'
            - name: MILVUS_USERNAME
              value: root
            - name: MILVUS_PASSWORD
              value: Milvus
            - name: SCORE_THRESHOLD
              value: '0.90'
            - name: MILVUS_COLLECTION
              value: california_driver_handbook_1_0
            - name: MAX_RETRIEVED_DOCS
              value: '4'
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600