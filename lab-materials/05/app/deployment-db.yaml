apiVersion: apps/v1
kind: Deployment
metadata:
  name: claimdb
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  selector:
    matchLabels:
      app: claimdb
  replicas: 1
  template:
    metadata:
      labels:
        app: claimdb
    spec:
      containers:
        - name: postgresql
          image: registry.redhat.io/rhel9/postgresql-13:latest
          resources:
            limits:
              memory: 512Mi
          readinessProbe:
            exec:
              command:
                - /usr/libexec/check-container
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          livenessProbe:
            exec:
              command:
                - /usr/libexec/check-container
                - '--live'
            initialDelaySeconds: 120
            timeoutSeconds: 10
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: POSTGRESQL_USER
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-user
            - name: POSTGRESQL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-password
            - name: POSTGRESQL_DATABASE
              valueFrom:
                secretKeyRef:
                  name: claimdb
                  key: database-name
          securityContext:
            capabilities: {}
            privileged: false
          ports:
            - containerPort: 5432
              protocol: TCP
          imagePullPolicy: IfNotPresent
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: claimdb-data
              mountPath: /var/lib/pgsql/data
      volumes:
        - name: claimdb-data
          persistentVolumeClaim:
            claimName: claimdb
  strategy:
    type: Recreate
