{"cells": [{"cell_type": "markdown", "id": "4ad2cc4e-31ec-4648-b0fe-6632f2bdbc36", "metadata": {"tags": []}, "source": ["## Extending the capabilities of our model\n", "\n", "An LLM is a very capable tool, but only to the extent of the knowledge or information it has been trained on. After all, you only know what you know, right? But what if you need to ask a question that is not in the training data? Or what if you need to ask a question that is not in the training data, but is related to it?\n", "\n", "There are different ways to solve this problem, depending on the resources you have and the time or money you can spend on it. Here are a few options:\n", "\n", "- Fully retrain the model to include the information you need. For an LLM, it's only possible for a handful of companies in the world that can afford literally thousands of GPUs running for weeks.\n", "- Fine-tune the model with this new information. This requires way less resources, and can usually be done in a few hours or minutes (depending on the size of the model). However as it does not fully retrain the model, the new information may not be completely integrated in the answers. Fine-tuning excels at giving a better understanding of a specific context or vocabulary, a little bit less on injecting new knowledge. Plus you have to retrain and redeploy the model anyway any time you want to add more information.\n", "- Put this new information in a database and have the parts relevant to the query retrieved and added to this query as a context before sending it to the LLM. This technique is called **Retrieval Augmented Generation, or RAG**. It is interesting as you don't have to retrain or fine-tune the model to benefit of this new knowledge, that you can easily update at any time.\n", "\n", "We have already prepared a Vector Database using [Milvus](https://milvus.io/), where we have stored (in the form of [Embeddings](https://www.ibm.com/topics/embedding)) the content of the [California Driver's Handbook](https://www.dmv.ca.gov/portal/handbook/california-driver-handbook/).\n", "\n", "In this Notebook, we are going to use RAG to **make some queries about a Claim** and see how this new knowledge can help without having to modify our LLM."]}, {"cell_type": "markdown", "id": "2a4e2b81-0e10-4390-a7b8-5ddfda53a3e3", "metadata": {}, "source": ["### Requirements and Imports\n", "\n", "If you have selected the right workbench image to launch as per the Lab's instructions, you should already have all the needed libraries. If not uncomment the first line in the next cell to install all the right packages."]}, {"cell_type": "code", "execution_count": null, "id": "d61c595d-967e-47de-a598-02b5d1ccec85", "metadata": {"tags": []}, "outputs": [], "source": ["# Uncomment the following line only if you have not selected the right workbench image, or are using this notebook outside of the workshop environment.\n", "# !pip install --no-cache-dir --no-dependencies --disable-pip-version-check -r requirements.txt\n", "\n", "import json\n", "\n", "import transformers\n", "from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler\n", "from langchain.chains import create_retrieval_chain\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.chat import SystemMessagePromptTemplate, HumanMessagePromptTemplate\n", "from langchain_huggingface import HuggingFaceEmbeddings\n", "from milvus_retriever_with_score_threshold import \\\n", "    MilvusRetrieverWithScoreThreshold\n", "\n", "# Turn off warnings when downloading the embedding model\n", "transformers.logging.set_verbosity_error()"]}, {"cell_type": "markdown", "id": "c428fbad-2345-4536-b687-72416d6b9b15", "metadata": {}, "source": ["### Langchain elements\n", "\n", "Again, we are going to use Langchain to define our task pipeline.\n", "\n", "First, the **LLM** where we will send our queries."]}, {"cell_type": "code", "execution_count": null, "id": "77f95a70-89fb-4e21-a51c-24e862b7953e", "metadata": {"tags": []}, "outputs": [], "source": ["# LLM Inference Server URL\n", "inference_server_url = \"http://granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local:8080\"\n", "\n", "# LLM definition\n", "llm = ChatOpenAI(\n", "    openai_api_key=\"EMPTY\",   # Private model, we don't need a key\n", "    openai_api_base=f\"{inference_server_url}/v1\",\n", "    model_name=\"granite-3-1-8b-instruct\",\n", "    temperature=0.01,\n", "    max_tokens=512,\n", "    streaming=True,\n", "    callbacks=[StreamingStdOutCallbackHandler()],\n", "    top_p=0.9,\n", "    presence_penalty=0.5,\n", "    model_kwargs={\n", "        \"stream_options\": {\"include_usage\": True}\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "4fa13907-14f1-4995-9756-8778c19a2101", "metadata": {}, "source": ["Then the connection to the **vector database** where we have prepared and stored the California Driver Handbook."]}, {"cell_type": "code", "execution_count": null, "id": "f849c1a0-7fe5-425f-853d-6a9e67a38971", "metadata": {"tags": []}, "outputs": [], "source": ["# First we define the embeddings that we used to process the Handbook\n", "model_kwargs = {\"trust_remote_code\": True}\n", "embeddings = HuggingFaceEmbeddings(\n", "    model_name=\"nomic-ai/nomic-embed-text-v1\",\n", "    model_kwargs=model_kwargs,\n", "    show_progress=False,\n", ")\n", "\n", "# Then we define the retriever that will fetch the relevant data from the Milvus vector store\n", "retriever = MilvusRetrieverWithScoreThreshold(\n", "    embedding_function=embeddings,\n", "    collection_name=\"california_driver_handbook_1_0\",\n", "    collection_description=\"\",\n", "    collection_properties=None,\n", "    connection_args={\n", "        \"uri\": \"http://vectordb-milvus.ic-shared-milvus.svc.cluster.local:19530\",\n", "        \"user\": \"root\",\n", "        \"password\": \"<PERSON><PERSON><PERSON><PERSON>\",\n", "        \"db_name\": \"default\"\n", "    },\n", "    consistency_level=\"Session\",\n", "    search_params=None,\n", "    k=4,\n", "    score_threshold=0.99,\n", "    enable_dynamic_field=True,\n", "    text_field=\"page_content\",\n", ")"]}, {"cell_type": "markdown", "id": "20b950bc-4d73-49e5-a35b-083a784edd50", "metadata": {}, "source": ["We will now define the **template** to use to make our query. Note that this template now contains a **References** section. That's were the documents returned from the vector database will be injected."]}, {"cell_type": "code", "execution_count": null, "id": "f8bb7517-faa2-43ed-a95d-835de975f916", "metadata": {"tags": []}, "outputs": [], "source": ["template = ChatPromptTemplate.from_messages([\n", "    SystemMessagePromptTemplate.from_template(\"\"\"\n", "        You are a helpful, respectful and honest assistant named \"<PERSON>sol Assistant\".\n", "        You will be given a claim summary, references to provide you with information, and a question.\n", "        You must answer the question based as much as possible on this claim with the help of the references.\n", "        Always answer as helpfully as possible, while being safe.\n", "        Your answers should not include any harmful, unethical, racist, sexist, toxic, dangerous, or illegal content.\n", "        Please ensure that your responses are socially unbiased and positive in nature.\n", "        If a question does not make any sense, or is not factually coherent, explain why instead of answering something not correct.\n", "        If you don't know the answer to a question, please don't share false information.\n", "        \"\"\"),\n", "    HumanMessagePromptTemplate.from_template(\"\"\"\n", "        Claim information:\n", "        {claim}\n", "\n", "        References:\n", "        {context}\n", "\n", "        Question: {input}\n", "        \"\"\")\n", "])"]}, {"cell_type": "markdown", "id": "849fbd67-220c-4a02-8e4e-7e0d1aa91588", "metadata": {}, "source": ["We are now ready to query the model!\n", "\n", "In the `claims` folder we have JSON files with examples of claims that could be received. We are going to read the first claim and ask a question related to it."]}, {"cell_type": "code", "execution_count": null, "id": "ca714bca-7cec-4afc-b275-fa389c05a993", "metadata": {"tags": []}, "outputs": [], "source": ["# Read the claim and put its content in the \"claim\" variable\n", "\n", "filename = 'claims/claim1.json'\n", "\n", "# Opening JSON file\n", "with open(filename, 'r') as file:\n", "    data = json.load(file)\n", "claim = data[\"content\"]"]}, {"cell_type": "markdown", "id": "6c4cde40-3571-4e3c-9b05-78389765c98f", "metadata": {}, "source": ["### First test, no additional knowledge\n", "\n", "Let's start with a first query about the claim, but without help from our vector database."]}, {"cell_type": "code", "execution_count": null, "id": "3ac398b4-d555-45e5-aab9-d9b319f07108", "metadata": {"tags": []}, "outputs": [], "source": ["# Create and send our query.\n", "\n", "query = \"Was <PERSON> allowed to pass at the red light?\"\n", "\n", "# Quick hack to reuse the same template with a different type of query.\n", "prompt = template.invoke({\"claim\": claim, \"context\": \"\", \"input\": query})\n", "resp = llm.invoke(input=prompt);"]}, {"cell_type": "markdown", "id": "2a0f714d-c6e7-4220-a16b-fc65dbae91fb", "metadata": {}, "source": ["We can see that the answer is valid. Here the model is using its general understanding of traffic regulation."]}, {"cell_type": "markdown", "id": "4f4e9a93-9b81-424a-96a9-f447e417c8c1", "metadata": {}, "source": ["### Second test, with added knowledge\n", "\n", "We will use the same prompt and query, but this time the model will have access to some references from the California's Driver Handbook."]}, {"cell_type": "code", "execution_count": null, "id": "dac009d5-d558-4258-9735-4fb0de46c309", "metadata": {"tags": []}, "outputs": [], "source": ["# Create and send our query.\n", "\n", "query = \"Was <PERSON> allowed to pass at the red light?\"\n", "\n", "# Instantiate RAG chain\n", "document_chain = create_stuff_documents_chain(llm, template)\n", "rag_chain = create_retrieval_chain(retriever, document_chain)\n", "\n", "resp = rag_chain.invoke({\n", "    \"input\": query,\n", "    \"claim\": claim\n", "})"]}, {"cell_type": "markdown", "id": "8c5659f0-a27f-4b9e-8dd1-e05f37671c8f", "metadata": {}, "source": ["That is pretty neat! Now the model refers more precisely to the rules that must be observed.\n", "\n", "But where did we get this information from? We can look into the sources associated with the answers from the vector database."]}, {"cell_type": "code", "execution_count": null, "id": "6e424c52-9a1a-4425-a105-4e0744ec0da6", "metadata": {"tags": []}, "outputs": [], "source": ["def format_sources(input_list):\n", "    sources = \"\"\n", "    if len(input_list) != 0:\n", "        sources += input_list[0].metadata[\"metadata\"][\"source\"] + ', page: ' + str(input_list[0].metadata[\"metadata\"][\"page\"])\n", "        page_list = [input_list[0].metadata[\"metadata\"][\"page\"]]\n", "        for item in input_list:\n", "            if item.metadata[\"metadata\"][\"page\"] not in page_list:  # Avoid duplicates\n", "                page_list.append(item.metadata[\"metadata\"][\"page\"])\n", "                sources += ', ' + str(item.metadata[\"metadata\"][\"page\"])\n", "    return sources\n", "\n", "\n", "results = format_sources(resp['context'])\n", "\n", "print(results)"]}, {"cell_type": "markdown", "id": "daf8cd32-0bdb-484d-a8bd-fb108ce2f131", "metadata": {}, "source": ["That's it! We now know how to complement our LLM with some external knowledge!"]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}