apiVersion: tekton.dev/v1beta1
kind: PipelineRun
metadata:
  name: confidence-check
  annotations:
    tekton.dev/output_artifacts: '{"run-a-file": [{"key": "artifacts/$PIPELINERUN/run-a-file/mlpipeline-metrics.tgz",
      "name": "mlpipeline-metrics", "path": "/tmp/mlpipeline-metrics.json"}, {"key":
      "artifacts/$PIPELINERUN/run-a-file/mlpipeline-ui-metadata.tgz", "name": "mlpipeline-ui-metadata",
      "path": "/tmp/mlpipeline-ui-metadata.json"}], "run-a-file-2": [{"key": "artifacts/$PIPELINERUN/run-a-file-2/mlpipeline-metrics.tgz",
      "name": "mlpipeline-metrics", "path": "/tmp/mlpipeline-metrics.json"}, {"key":
      "artifacts/$PIPELINERUN/run-a-file-2/mlpipeline-ui-metadata.tgz", "name": "mlpipeline-ui-metadata",
      "path": "/tmp/mlpipeline-ui-metadata.json"}], "run-a-file-3": [{"key": "artifacts/$PIPELINERUN/run-a-file-3/mlpipeline-metrics.tgz",
      "name": "mlpipeline-metrics", "path": "/tmp/mlpipeline-metrics.json"}, {"key":
      "artifacts/$PIPELINERUN/run-a-file-3/mlpipeline-ui-metadata.tgz", "name": "mlpipeline-ui-metadata",
      "path": "/tmp/mlpipeline-ui-metadata.json"}], "run-a-file-4": [{"key": "artifacts/$PIPELINERUN/run-a-file-4/mlpipeline-metrics.tgz",
      "name": "mlpipeline-metrics", "path": "/tmp/mlpipeline-metrics.json"}, {"key":
      "artifacts/$PIPELINERUN/run-a-file-4/mlpipeline-ui-metadata.tgz", "name": "mlpipeline-ui-metadata",
      "path": "/tmp/mlpipeline-ui-metadata.json"}]}'
    tekton.dev/input_artifacts: '{}'
    tekton.dev/artifact_bucket: mlpipeline
    tekton.dev/artifact_endpoint: minio-service.kubeflow:9000
    tekton.dev/artifact_endpoint_scheme: http://
    tekton.dev/artifact_items: '{"run-a-file": [["mlpipeline-metrics", "/tmp/mlpipeline-metrics.json"],
      ["mlpipeline-ui-metadata", "/tmp/mlpipeline-ui-metadata.json"]], "run-a-file-2":
      [["mlpipeline-metrics", "/tmp/mlpipeline-metrics.json"], ["mlpipeline-ui-metadata",
      "/tmp/mlpipeline-ui-metadata.json"]], "run-a-file-3": [["mlpipeline-metrics",
      "/tmp/mlpipeline-metrics.json"], ["mlpipeline-ui-metadata", "/tmp/mlpipeline-ui-metadata.json"]],
      "run-a-file-4": [["mlpipeline-metrics", "/tmp/mlpipeline-metrics.json"], ["mlpipeline-ui-metadata",
      "/tmp/mlpipeline-ui-metadata.json"]]}'
    sidecar.istio.io/inject: "false"
    tekton.dev/template: ''
    pipelines.kubeflow.org/big_data_passing_format: $(workspaces.$TASK_NAME.path)/artifacts/$ORIG_PR_NAME/$TASKRUN_NAME/$TASK_PARAM_NAME
    pipelines.kubeflow.org/pipeline_spec: '{"name": "confidence-check"}'
  labels:
    pipelines.kubeflow.org/pipelinename: ''
    pipelines.kubeflow.org/generation: ''
spec:
  pipelineSpec:
    tasks:
    - name: run-a-file
      taskSpec:
        steps:
        - name: main
          args:
          - |
            sh -c "mkdir -p ./jupyter-work-dir && cd ./jupyter-work-dir"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/bootstrapper.py' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/bootstrapper.py --output bootstrapper.py"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/requirements-elyra.txt' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/requirements-elyra.txt --output requirements-elyra.txt"
            sh -c "python3 -m pip install  packaging && python3 -m pip freeze > requirements-current.txt && python3 bootstrapper.py --pipeline-name 'confidence-check' --cos-endpoint 'http://minio.ic-shared-minio.svc:9000' --cos-bucket 'pipeline-bucket' --cos-directory 'confidence-check-0109084010' --cos-dependencies-archive 'test_response_quality-d0510f0c-fc50-42fc-a598-97e26b34ed88.tar.gz' --file 'parasol-insurance/lab-materials/03/06/test_response_quality.py' --outputs 'quality_result.json' "
          command:
          - sh
          - -c
          env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: AWS_ACCESS_KEY_ID
                name: aws-connection-shared-minio---pipelines
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: AWS_SECRET_ACCESS_KEY
                name: aws-connection-shared-minio---pipelines
          - name: ELYRA_RUNTIME_ENV
            value: kfp
          - name: ELYRA_ENABLE_PIPELINE_INFO
            value: "True"
          - name: ELYRA_WRITABLE_CONTAINER_DIR
            value: /tmp
          - name: ELYRA_RUN_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['pipelines.kubeflow.org/run_name']
          image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-confidence-pipeline:1.2
        stepTemplate:
          volumeMounts:
          - name: mlpipeline-metrics
            mountPath: /tmp
        volumes:
        - name: mlpipeline-metrics
          emptyDir: {}
        metadata:
          labels:
            elyra/node-type: notebook-script
            elyra/pipeline-name: confidence-check
            elyra/pipeline-version: ''
            elyra/experiment-name: ''
            elyra/node-name: test_response_quality
            pipelines.kubeflow.org/cache_enabled: "true"
          annotations:
            elyra/node-file-name: parasol-insurance/lab-materials/03/06/test_response_quality.py
            elyra/pipeline-source: confidence-check.pipeline
            pipelines.kubeflow.org/task_display_name: test_response_quality
            pipelines.kubeflow.org/component_spec_digest: '{"name": "Run a file",
              "outputs": [], "version": "Run a file@sha256=0ede2ced24a6127c78656498f8e9b13b6b1d1d8e42c8db43354771af59f60488"}'
    - name: run-a-file-2
      taskSpec:
        steps:
        - name: main
          args:
          - |
            sh -c "mkdir -p ./jupyter-work-dir && cd ./jupyter-work-dir"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/bootstrapper.py' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/bootstrapper.py --output bootstrapper.py"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/requirements-elyra.txt' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/requirements-elyra.txt --output requirements-elyra.txt"
            sh -c "python3 -m pip install  packaging && python3 -m pip freeze > requirements-current.txt && python3 bootstrapper.py --pipeline-name 'confidence-check' --cos-endpoint 'http://minio.ic-shared-minio.svc:9000' --cos-bucket 'pipeline-bucket' --cos-directory 'confidence-check-0109084010' --cos-dependencies-archive 'test_responsetime-c50bb14f-a036-4af1-b5dc-21e48eb80f7f.tar.gz' --file 'parasol-insurance/lab-materials/03/06/test_responsetime.py' --outputs 'responsetime_result.json' "
          command:
          - sh
          - -c
          env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: AWS_ACCESS_KEY_ID
                name: aws-connection-shared-minio---pipelines
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: AWS_SECRET_ACCESS_KEY
                name: aws-connection-shared-minio---pipelines
          - name: ELYRA_RUNTIME_ENV
            value: kfp
          - name: ELYRA_ENABLE_PIPELINE_INFO
            value: "True"
          - name: ELYRA_WRITABLE_CONTAINER_DIR
            value: /tmp
          - name: ELYRA_RUN_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['pipelines.kubeflow.org/run_name']
          image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-confidence-pipeline:1.2
        stepTemplate:
          volumeMounts:
          - name: mlpipeline-metrics
            mountPath: /tmp
        volumes:
        - name: mlpipeline-metrics
          emptyDir: {}
        metadata:
          labels:
            elyra/node-type: notebook-script
            elyra/pipeline-name: confidence-check
            elyra/pipeline-version: ''
            elyra/experiment-name: ''
            elyra/node-name: test_responsetime
            pipelines.kubeflow.org/cache_enabled: "true"
          annotations:
            elyra/node-file-name: parasol-insurance/lab-materials/03/06/test_responsetime.py
            elyra/pipeline-source: confidence-check.pipeline
            pipelines.kubeflow.org/task_display_name: test_responsetime
            pipelines.kubeflow.org/component_spec_digest: '{"name": "Run a file",
              "outputs": [], "version": "Run a file@sha256=fbcb6cccb2393e957b4b042a22dffff6d92d28a73efb5775c07bb82b7973cdf3"}'
    - name: run-a-file-3
      taskSpec:
        steps:
        - name: main
          args:
          - |
            sh -c "mkdir -p ./jupyter-work-dir && cd ./jupyter-work-dir"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/bootstrapper.py' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/bootstrapper.py --output bootstrapper.py"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/requirements-elyra.txt' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/requirements-elyra.txt --output requirements-elyra.txt"
            sh -c "python3 -m pip install  packaging && python3 -m pip freeze > requirements-current.txt && python3 bootstrapper.py --pipeline-name 'confidence-check' --cos-endpoint 'http://minio.ic-shared-minio.svc:9000' --cos-bucket 'pipeline-bucket' --cos-directory 'confidence-check-0109084010' --cos-dependencies-archive 'test_security-6b595dc7-afb8-46bb-bf52-7cd695ddafb8.tar.gz' --file 'parasol-insurance/lab-materials/03/06/test_security.py' --outputs 'security_result.json' "
          command:
          - sh
          - -c
          env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: AWS_ACCESS_KEY_ID
                name: aws-connection-shared-minio---pipelines
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: AWS_SECRET_ACCESS_KEY
                name: aws-connection-shared-minio---pipelines
          - name: ELYRA_RUNTIME_ENV
            value: kfp
          - name: ELYRA_ENABLE_PIPELINE_INFO
            value: "True"
          - name: ELYRA_WRITABLE_CONTAINER_DIR
            value: /tmp
          - name: ELYRA_RUN_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['pipelines.kubeflow.org/run_name']
          image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-confidence-pipeline:1.2
        stepTemplate:
          volumeMounts:
          - name: mlpipeline-metrics
            mountPath: /tmp
        volumes:
        - name: mlpipeline-metrics
          emptyDir: {}
        metadata:
          labels:
            elyra/node-type: notebook-script
            elyra/pipeline-name: confidence-check
            elyra/pipeline-version: ''
            elyra/experiment-name: ''
            elyra/node-name: test_security
            pipelines.kubeflow.org/cache_enabled: "true"
          annotations:
            elyra/node-file-name: parasol-insurance/lab-materials/03/06/test_security.py
            elyra/pipeline-source: confidence-check.pipeline
            pipelines.kubeflow.org/task_display_name: test_security
            pipelines.kubeflow.org/component_spec_digest: '{"name": "Run a file",
              "outputs": [], "version": "Run a file@sha256=911e07386ce6fbae48167dee9e6eb42f31497bc01b0ef69e3d3b40b4d203a6d0"}'
    - name: run-a-file-4
      taskSpec:
        steps:
        - name: main
          args:
          - |
            sh -c "mkdir -p ./jupyter-work-dir && cd ./jupyter-work-dir"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/bootstrapper.py' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/bootstrapper.py --output bootstrapper.py"
            sh -c "echo 'Downloading file:///opt/app-root/bin/utils/requirements-elyra.txt' && curl --fail -H 'Cache-Control: no-cache' -L file:///opt/app-root/bin/utils/requirements-elyra.txt --output requirements-elyra.txt"
            sh -c "python3 -m pip install  packaging && python3 -m pip freeze > requirements-current.txt && python3 bootstrapper.py --pipeline-name 'confidence-check' --cos-endpoint 'http://minio.ic-shared-minio.svc:9000' --cos-bucket 'pipeline-bucket' --cos-directory 'confidence-check-0109084010' --cos-dependencies-archive 'summarize_results-6b99ceae-d124-4758-904a-03e1a49fe56d.tar.gz' --file 'parasol-insurance/lab-materials/03/06/summarize_results.py' --inputs 'responsetime_result.json;security_result.json;quality_result.json' --outputs 'results.json' "
          command:
          - sh
          - -c
          env:
          - name: AWS_ACCESS_KEY_ID
            valueFrom:
              secretKeyRef:
                key: AWS_ACCESS_KEY_ID
                name: aws-connection-shared-minio---pipelines
          - name: AWS_SECRET_ACCESS_KEY
            valueFrom:
              secretKeyRef:
                key: AWS_SECRET_ACCESS_KEY
                name: aws-connection-shared-minio---pipelines
          - name: ELYRA_RUNTIME_ENV
            value: kfp
          - name: ELYRA_ENABLE_PIPELINE_INFO
            value: "True"
          - name: ELYRA_WRITABLE_CONTAINER_DIR
            value: /tmp
          - name: ELYRA_RUN_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.annotations['pipelines.kubeflow.org/run_name']
          image: quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-confidence-pipeline:1.2
        stepTemplate:
          volumeMounts:
          - name: mlpipeline-metrics
            mountPath: /tmp
        volumes:
        - name: mlpipeline-metrics
          emptyDir: {}
        metadata:
          labels:
            elyra/node-type: notebook-script
            elyra/pipeline-name: confidence-check
            elyra/pipeline-version: ''
            elyra/experiment-name: ''
            elyra/node-name: summarize_results
            pipelines.kubeflow.org/cache_enabled: "true"
          annotations:
            elyra/node-file-name: parasol-insurance/lab-materials/03/06/summarize_results.py
            elyra/pipeline-source: confidence-check.pipeline
            pipelines.kubeflow.org/task_display_name: summarize_results
            pipelines.kubeflow.org/component_spec_digest: '{"name": "Run a file",
              "outputs": [], "version": "Run a file@sha256=710a8e5ea145ddcf52ee1df00cbb79bc030fb9b139970cea9676a461974dcd0e"}'
      runAfter:
      - run-a-file
      - run-a-file-2
      - run-a-file-3
