{"doc_type": "pipeline", "version": "3.0", "json_schema": "http://api.dataplatform.ibm.com/schemas/common-pipeline/pipeline-flow/pipeline-flow-v3-schema.json", "id": "elyra-auto-generated-pipeline", "primary_pipeline": "primary", "pipelines": [{"id": "primary", "nodes": [{"id": "d0510f0c-fc50-42fc-a598-97e26b34ed88", "type": "execution_node", "op": "execute-python-node", "app_data": {"component_parameters": {"dependencies": ["llm_usage.py", "summary_template.txt", "example_text.txt"], "include_subdirectories": false, "outputs": ["quality_result.json"], "env_vars": [], "kubernetes_pod_annotations": [], "kubernetes_pod_labels": [], "kubernetes_secrets": [], "kubernetes_shared_mem_size": {}, "kubernetes_tolerations": [], "mounted_volumes": [], "filename": "test_response_quality.py"}, "label": "", "ui_data": {"label": "test_response_quality.py", "image": "/notebook/user2-auto/my-workbench/static/elyra/python.svg", "x_pos": 582, "y_pos": 143, "description": "Run Python script"}}, "inputs": [{"id": "inPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Input Port"}}}], "outputs": [{"id": "outPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Output Port"}}}]}, {"id": "c50bb14f-a036-4af1-b5dc-21e48eb80f7f", "type": "execution_node", "op": "execute-python-node", "app_data": {"component_parameters": {"dependencies": ["llm_usage.py"], "include_subdirectories": false, "outputs": ["responsetime_result.json"], "env_vars": [], "kubernetes_pod_annotations": [], "kubernetes_pod_labels": [], "kubernetes_secrets": [], "kubernetes_shared_mem_size": {}, "kubernetes_tolerations": [], "mounted_volumes": [], "filename": "test_responsetime.py"}, "label": "", "ui_data": {"label": "test_responsetime.py", "image": "/notebook/user2-auto/my-workbench/static/elyra/python.svg", "x_pos": 584, "y_pos": 299, "description": "Run Python script"}}, "inputs": [{"id": "inPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Input Port"}}}], "outputs": [{"id": "outPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Output Port"}}}]}, {"id": "6b595dc7-afb8-46bb-bf52-7cd695ddafb8", "type": "execution_node", "op": "execute-python-node", "app_data": {"component_parameters": {"dependencies": [], "include_subdirectories": false, "outputs": ["security_result.json"], "env_vars": [], "kubernetes_pod_annotations": [], "kubernetes_pod_labels": [], "kubernetes_secrets": [], "kubernetes_shared_mem_size": {}, "kubernetes_tolerations": [], "mounted_volumes": [], "filename": "test_security.py"}, "label": "", "ui_data": {"label": "test_security.py", "image": "/notebook/user2-auto/my-workbench/static/elyra/python.svg", "x_pos": 585, "y_pos": 437, "description": "Run Python script"}}, "inputs": [{"id": "inPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Input Port"}}}], "outputs": [{"id": "outPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Output Port"}}}]}, {"id": "6b99ceae-d124-4758-904a-03e1a49fe56d", "type": "execution_node", "op": "execute-python-node", "app_data": {"component_parameters": {"dependencies": [], "include_subdirectories": false, "outputs": ["results.json"], "env_vars": [], "kubernetes_pod_annotations": [], "kubernetes_pod_labels": [], "kubernetes_secrets": [], "kubernetes_shared_mem_size": {}, "kubernetes_tolerations": [], "mounted_volumes": [], "filename": "summarize_results.py"}, "label": "", "ui_data": {"label": "summarize_results.py", "image": "/notebook/user2-auto/my-workbench/static/elyra/python.svg", "x_pos": 977, "y_pos": 295, "description": "Run Python script"}}, "inputs": [{"id": "inPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Input Port"}}, "links": [{"id": "a2440b4e-785d-4963-b4e7-ef939be378c1", "node_id_ref": "d0510f0c-fc50-42fc-a598-97e26b34ed88", "port_id_ref": "outPort"}, {"id": "e896bb1d-ef18-4076-b038-b196f04809bf", "node_id_ref": "c50bb14f-a036-4af1-b5dc-21e48eb80f7f", "port_id_ref": "outPort"}, {"id": "9a795afd-f08b-4cc2-94de-2005482041e8", "node_id_ref": "6b595dc7-afb8-46bb-bf52-7cd695ddafb8", "port_id_ref": "outPort"}]}], "outputs": [{"id": "outPort", "app_data": {"ui_data": {"cardinality": {"min": 0, "max": -1}, "label": "Output Port"}}}]}], "app_data": {"ui_data": {"comments": []}, "version": 8, "runtime_type": "KUBEFLOW_PIPELINES", "properties": {"name": "confidence-check", "runtime": "Data Science Pipelines", "pipeline_defaults": {"kubernetes_tolerations": [], "kubernetes_pod_annotations": [], "kubernetes_shared_mem_size": {}, "kubernetes_pod_labels": [], "mounted_volumes": [], "kubernetes_secrets": [], "env_vars": [], "runtime_image": "quay.io/rh-aiservices-bu/rhoai-lab-insurance-claim-confidence-pipeline:2.0"}}}, "runtime_ref": ""}], "schemas": []}