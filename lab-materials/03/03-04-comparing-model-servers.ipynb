{"cells": [{"cell_type": "markdown", "id": "4ad2cc4e-31ec-4648-b0fe-6632f2bdbc36", "metadata": {}, "source": ["## Comparing models for our different tasks\n", "\n", "In this Notebook, we are going to use another model, **Qwen2.5-0.5B-w8a8** in parallel to **Granite-3.1-8B-Instruct** and see how it behaves.\n", "\n", "Qwen2.5-0.5B-w8a8 is a much smaller and quantized model (think like \"compressed\"), but it will run without a GPU and use only 5 GB of RAM. But is it up to the task?"]}, {"cell_type": "markdown", "id": "2a4e2b81-0e10-4390-a7b8-5ddfda53a3e3", "metadata": {}, "source": ["### Requirements and Imports\n", "\n", "If you have selected the right workbench image to launch as per the Lab's instructions, you should already have all the needed libraries. If not uncomment the first line in the next cell to install all the right packages."]}, {"cell_type": "code", "execution_count": null, "id": "d61c595d-967e-47de-a598-02b5d1ccec85", "metadata": {"tags": []}, "outputs": [], "source": ["# Uncomment the following line only if you have not selected the right workbench image, or are using this notebook outside of the workshop environment.\n", "# !pip install --no-cache-dir --no-dependencies --disable-pip-version-check -r requirements.txt\n", "\n", "import json\n", "import time\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.chat import SystemMessagePromptTemplate, HumanMessagePromptTemplate\n", "from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler"]}, {"cell_type": "markdown", "id": "c428fbad-2345-4536-b687-72416d6b9b15", "metadata": {}, "source": ["### Langchain pipeline\n", "\n", "We are now going to define two different LLM endpoints, and two different Langchain pipelines."]}, {"cell_type": "code", "execution_count": null, "id": "77f95a70-89fb-4e21-a51c-24e862b7953e", "metadata": {"tags": []}, "outputs": [], "source": ["# LLM Inference Server URL\n", "inference_server_url = \"http://granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local:8080\"\n", "\n", "# LLM definition\n", "llm = ChatOpenAI(\n", "    openai_api_key=\"EMPTY\",   # Private model, we don't need a key\n", "    openai_api_base=f\"{inference_server_url}/v1\",\n", "    model_name=\"granite-3-1-8b-instruct\",\n", "    temperature=0.01,\n", "    max_tokens=512,\n", "    streaming=True,\n", "    callbacks=[StreamingStdOutCallbackHandler()],\n", "    top_p=0.9,\n", "    presence_penalty=0.5,\n", "    model_kwargs={\n", "        \"stream_options\": {\"include_usage\": True}\n", "    }\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "782046b7-f0a4-487c-86fc-3131e668c6c0", "metadata": {"tags": []}, "outputs": [], "source": ["# Qwen2.5 LLM Inference Server URL\n", "inference_server_url_qwen = \"http://qwen-predictor.ic-shared-llm.svc.cluster.local:8080\"\n", "\n", "# LLM definition\n", "llm_qwen = ChatOpenAI(\n", "    openai_api_key=\"EMPTY\",   # Private model, we don't need a key\n", "    openai_api_base=f\"{inference_server_url_qwen}/v1\",\n", "    model_name=\"qwen2.5\",\n", "    temperature=0.01,\n", "    max_tokens=128,\n", "    streaming=True,\n", "    callbacks=[StreamingStdOutCallbackHandler()],\n", "    top_p=0.9,\n", "    presence_penalty=0.5,\n", "    model_kwargs={\n", "        \"stream_options\": {\"include_usage\": True}\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "20b950bc-4d73-49e5-a35b-083a784edd50", "metadata": {}, "source": ["The **template** will be the same for both models."]}, {"cell_type": "code", "execution_count": null, "id": "f8bb7517-faa2-43ed-a95d-835de975f916", "metadata": {"tags": []}, "outputs": [], "source": ["template = ChatPromptTemplate.from_messages([\n", "    SystemMessagePromptTemplate.from_template(\n", "        \"\"\"You are a helpful, respectful and honest assistant.\n", "        Always assist with care, respect, and truth. Respond with utmost utility yet securely.\n", "        Avoid harmful, unethical, prejudiced, or negative content. Ensure replies promote fairness and positivity.\n", "        I will give you a text, then ask a question about it. Give a precise and as concise as possible answer to this question.\n", "        \"\"\"),\n", "    HumanMessagePromptTemplate.from_template(\n", "        \"\"\"### ### TEXT:\n", "        {text}\n", "\n", "        ### QUESTION:\n", "        {query}\n", "\n", "        ### ANSWER:\"\"\"\n", "    )\n", "])"]}, {"cell_type": "markdown", "id": "849fbd67-220c-4a02-8e4e-7e0d1aa91588", "metadata": {}, "source": ["We are now ready to query the models!\n", "\n", "In this example, we are only going to query one claim and see what happens. Of course, feel free to try with different ones."]}, {"cell_type": "code", "execution_count": null, "id": "dac009d5-d558-4258-9735-4fb0de46c309", "metadata": {"tags": []}, "outputs": [], "source": ["filename = 'claims/claim1.json'\n", "\n", "# Opening JSON file\n", "claims = {}\n", "with open(filename, 'r') as file:\n", "    data = json.load(file)\n", "claims[filename] = data\n", "\n", "# Content and queries\n", "text_input = f\"Subject: {claims[filename]['subject']}\\nContent:\\n{claims[filename]['content']}\"\n", "sentiment_query = \"What is the sentiment of the person sending this claim?\"\n", "location_query = \"Where does the event the claim is related to happen?\"\n", "time_query = \"When does the event the claim is related to happen?\"\n", "\n", "# Analyze the claim\n", "print(\"***************************\")\n", "print(f\"* Claim: {filename}\")\n", "print(\"***************************\")\n", "print(\"Original content:\")\n", "print(\"-----------------\")\n", "print(f\"Subject: {claims[filename]['subject']}\\nContent:\\n{claims[filename]['content']}\\n\\n\")\n", "print('Analysis with Granite-3.1-8B-Instruct:')\n", "print(\"--------\")\n", "start_granite = time.time()\n", "print(\"- Sentiment: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": sentiment_query})\n", "sentiment_granite = llm.invoke(input=prompt);\n", "print(\"\\n- Location: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": location_query})\n", "location_granite = llm.invoke(input=prompt);\n", "print(\"\\n- Time: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": time_query})\n", "time_granite = llm.invoke(input=prompt);\n", "print(\"\\n\\n                          ----====----\\n\")\n", "end_granite = time.time()\n", "print('Analysis with <PERSON><PERSON>:')\n", "print(\"--------\")\n", "start_qwen = time.time()\n", "print(\"- Sentiment: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": sentiment_query})\n", "sentiment_qwen = llm_qwen.invoke(input=prompt);\n", "print(\"\\n- Location: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": location_query})\n", "location_qwen = llm_qwen.invoke(input=prompt);\n", "print(\"\\n- Time: \")\n", "prompt = template.invoke({\"text\": text_input, \"query\": time_query})\n", "time_qwen = llm_qwen.invoke(input=prompt);\n", "print(\"\\n\\n                          ----====----\\n\")\n", "end_qwen = time.time()\n", "\n", "print(f\"Granite analysis time: {end_granite - start_granite:.2f} seconds\")\n", "print(f\"Qwen analysis time: {end_qwen - start_qwen:.2f} seconds\")\n", "print(\"\\n\\n                          ----====----\\n\")"]}, {"cell_type": "markdown", "id": "6e28a5b0-6c93-42ba-84dd-42e17746d11d", "metadata": {}, "source": ["As you can see, **Qwen2.5-0.5B-w8a8** can faitly be run on a CPU as it's a compressed 500 Million parameters model only. However those results may be less accurate or detailed, even contain hallucinations and unneeded text, and take much more time. So it works to some extent, but the results are nowhere near the ones from Granite-3.1-8B-Instruct, which is an 8 Billion parameter running on a GPU.\n", "\n", "The art of working with LLM is to find the right balance between the performance and accuracy you require, and the resources it take, along with the involved costs.\n", "\n", "Therefore it's important to have confidence checks in place to make sure that as your data changes, or your model evolves, you always get the behaviour you expect."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}