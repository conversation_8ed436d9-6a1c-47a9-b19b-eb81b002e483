{"cells": [{"cell_type": "markdown", "id": "4ad2cc4e-31ec-4648-b0fe-6632f2bdbc36", "metadata": {}, "source": ["## Extracting information from a text using an LLM\n", "\n", "As an LLM \"understands\" a language, it can be suited for tasks like sentiment analysis or information extraction.\n", "\n", "In this Notebook, we are going to use our LLM to analyze the claims to find the state of mind of the person writing, and the location and time of the accident."]}, {"cell_type": "markdown", "id": "2a4e2b81-0e10-4390-a7b8-5ddfda53a3e3", "metadata": {}, "source": ["### Requirements and Imports\n", "\n", "If you have selected the right workbench image to launch as per the Lab's instructions, you should already have all the needed libraries. If not uncomment the first line in the next cell to install all the right packages."]}, {"cell_type": "code", "execution_count": null, "id": "d61c595d-967e-47de-a598-02b5d1ccec85", "metadata": {"tags": []}, "outputs": [], "source": ["# Uncomment the following line only if you have not selected the right workbench image, or are using this notebook outside of the workshop environment.\n", "# !pip install --no-cache-dir --no-dependencies --disable-pip-version-check -r requirements.txt\n", "\n", "import json\n", "import os\n", "from os import listdir\n", "from os.path import isfile, join\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.chat import SystemMessagePromptTemplate, HumanMessagePromptTemplate\n", "from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler"]}, {"cell_type": "markdown", "id": "c428fbad-2345-4536-b687-72416d6b9b15", "metadata": {}, "source": ["### Langchain pipeline\n", "\n", "Again, we are going to use Langchain to define our task pipeline."]}, {"cell_type": "code", "execution_count": null, "id": "77f95a70-89fb-4e21-a51c-24e862b7953e", "metadata": {"tags": []}, "outputs": [], "source": ["# LLM Inference Server URL\n", "inference_server_url = \"http://granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local:8080\"\n", "\n", "# LLM definition\n", "llm = ChatOpenAI(\n", "    openai_api_key=\"EMPTY\",   # Private model, we don't need a key\n", "    openai_api_base=f\"{inference_server_url}/v1\",\n", "    model_name=\"granite-3-1-8b-instruct\",\n", "    temperature=0.01,\n", "    max_tokens=512,\n", "    streaming=True,\n", "    callbacks=[StreamingStdOutCallbackHandler()],\n", "    top_p=0.9,\n", "    presence_penalty=0.5,\n", "    model_kwargs={\n", "        \"stream_options\": {\"include_usage\": True}\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "20b950bc-4d73-49e5-a35b-083a784edd50", "metadata": {}, "source": ["We will now define a **template** to use specifically with the different tasks.\n", "\n", "Notice that this time we have **two placeholders** to be able to resuse the same template for different questions."]}, {"cell_type": "code", "execution_count": null, "id": "f8bb7517-faa2-43ed-a95d-835de975f916", "metadata": {"tags": []}, "outputs": [], "source": ["template = ChatPromptTemplate.from_messages([\n", "    SystemMessagePromptTemplate.from_template(\n", "        \"\"\"You are a helpful, respectful and honest assistant.\n", "        Always assist with care, respect, and truth. Respond with utmost utility yet securely.\n", "        Avoid harmful, unethical, prejudiced, or negative content. Ensure replies promote fairness and positivity.\n", "        I will give you a text, then ask a question about it. Give a precise and as concise as possible answer to this question.\n", "        \"\"\"),\n", "    HumanMessagePromptTemplate.from_template(\n", "        \"\"\"### ### TEXT:\n", "        {text}\n", "\n", "        ### QUESTION:\n", "        {query}\n", "\n", "        ### ANSWER:\"\"\"\n", "    )\n", "])"]}, {"cell_type": "markdown", "id": "849fbd67-220c-4a02-8e4e-7e0d1aa91588", "metadata": {}, "source": ["We are now ready to query the model!\n", "\n", "In the `claims` folder we have JSON files with examples of claims that could be received. We are going to read those files, display them, then the analysis that the LLM made."]}, {"cell_type": "code", "execution_count": null, "id": "ca714bca-7cec-4afc-b275-fa389c05a993", "metadata": {"tags": []}, "outputs": [], "source": ["# Read the claims and populate a dictionary\n", "\n", "claims_path = 'claims'\n", "onlyfiles = [f for f in listdir(claims_path) if isfile(join(claims_path, f))]\n", "\n", "claims = {}\n", "\n", "for filename in onlyfiles:\n", "    # Opening JSON file\n", "    with open(os.path.join(claims_path, filename), 'r') as file:\n", "        data = json.load(file)\n", "    claims[filename] = data"]}, {"cell_type": "code", "execution_count": null, "id": "dac009d5-d558-4258-9735-4fb0de46c309", "metadata": {"tags": []}, "outputs": [], "source": ["# Analyze the claims\n", "\n", "for filename in onlyfiles:\n", "    print(\"***************************\")\n", "    print(f\"* Claim: {filename}\")\n", "    print(\"***************************\")\n", "    print(\"Original content:\")\n", "    print(\"-----------------\")\n", "    print(f\"Subject: {claims[filename]['subject']}\\nContent:\\n{claims[filename]['content']}\\n\\n\")\n", "    print('Analysis:')\n", "    print(\"--------\")\n", "    text_input = f\"Subject: {claims[filename]['subject']}\\nContent:\\n{claims[filename]['content']}\"\n", "    sentiment_query = \"What is the sentiment of the person sending this claim?\"\n", "    location_query = \"Where does the event the claim is related to happen?\"\n", "    time_query = \"When does the event the claim is related to happen? If possible, specify the date and the time.\"\n", "    print(\"- Sentiment: \")\n", "    prompt = template.invoke({\"text\": text_input, \"query\": sentiment_query})\n", "    sentiment = llm.invoke(input=prompt);\n", "    print(\"\\n- Location: \")\n", "    prompt = template.invoke({\"text\": text_input, \"query\": location_query})\n", "    location = llm.invoke(input=prompt);\n", "    print(\"\\n- Time: \")\n", "    prompt = template.invoke({\"text\": text_input, \"query\": time_query})\n", "    time = llm.invoke(input=prompt);\n", "    print(\"\\n\\n                          ----====----\\n\")"]}, {"cell_type": "markdown", "id": "6e28a5b0-6c93-42ba-84dd-42e17746d11d", "metadata": {}, "source": ["You can come back to this notebook at section 3.7 for some optional exercises if you want."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}