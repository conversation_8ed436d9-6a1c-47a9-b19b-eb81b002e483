{"cells": [{"cell_type": "markdown", "id": "4ad2cc4e-31ec-4648-b0fe-6632f2bdbc36", "metadata": {}, "source": ["## Working with an LLM programmatically\n", "\n", "You have certainly interacted before with a Large Language Model (LLM) like ChatGPT. This is usually done through a UI or an application.\n", "\n", "In this Notebook, we are going to use Python to connect and query an LLM directly through its API. For this Lab we have selected the model **Granite-3.1-8B-Instruct**.(https://huggingface.co/RedHatAI/granite-3.1-8b-instruct). This is a fully Open Source model (Apache 2.0 license) developed by IBM Research.\n", "\n", "This model has already been deployed on the Lab cluster because even if it's a smaller model, it still needs a GPU with 24GB of RAM to run..."]}, {"cell_type": "markdown", "id": "2a4e2b81-0e10-4390-a7b8-5ddfda53a3e3", "metadata": {}, "source": ["### Requirements and Imports\n", "\n", "If you have selected the right workbench image to launch as per the Lab's instructions, you should already have all the needed libraries. If not uncomment the first line in the next cell to install all the right packages. We will then import the libraries we need."]}, {"cell_type": "code", "execution_count": null, "id": "d61c595d-967e-47de-a598-02b5d1ccec85", "metadata": {"tags": []}, "outputs": [], "source": ["# Uncomment the following line only if you have not selected the right workbench image, or are using this notebook outside of the workshop environment.\n", "# !pip install --no-cache-dir --no-dependencies --disable-pip-version-check -r requirements.txt\n", "import json\n", "\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.prompts.chat import SystemMessagePromptTemplate, HumanMessagePromptTemplate\n", "from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler"]}, {"cell_type": "markdown", "id": "c428fbad-2345-4536-b687-72416d6b9b15", "metadata": {}, "source": ["### Langchain\n", "\n", "Langchain (https://www.langchain.com/) is a framework for developing applications powered by language models. It will take care for us of all the boilerplate code we would have to manually write to properly query an LLM API.\n", "\n", "We will start by creating an **llm** instance, defined by the location where the LLM API can be queried and some parameters that will be applied to the model. For example, `max_new_tokens` will instruct the model to answer with a maximum of 512 tokens (words or parts of words). `temperature`, set really low here, will instruct the model to stay truth-grounded, and not try to be too \"creative\". After all, we're not trying to write a fancy poem here!"]}, {"cell_type": "code", "execution_count": null, "id": "77f95a70-89fb-4e21-a51c-24e862b7953e", "metadata": {"tags": []}, "outputs": [], "source": ["# LLM Inference Server URL\n", "inference_server_url = \"http://granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local:8080\"\n", "\n", "# LLM definition\n", "llm = ChatOpenAI(\n", "    openai_api_key=\"EMPTY\",   # Private model, we don't need a key\n", "    openai_api_base=f\"{inference_server_url}/v1\",\n", "    model_name=\"granite-3-1-8b-instruct\",\n", "    temperature=0.01,\n", "    max_tokens=512,\n", "    streaming=True,\n", "    callbacks=[StreamingStdOutCallbackHandler()],\n", "    top_p=0.9,\n", "    presence_penalty=0.5,\n", "    model_kwargs={\n", "        \"stream_options\": {\"include_usage\": True}\n", "    }\n", ")"]}, {"cell_type": "markdown", "id": "20b950bc-4d73-49e5-a35b-083a784edd50", "metadata": {}, "source": ["We also need a **template** to be applied to every request we are sending to the model (the \"Prompt\").\n", "\n", "When querying a model, you almost never want to send directly what the user has typed. On top of this entry, you need to give proper instructions to the model so that it knows how to handle it: what and how to answer, what NOT to answer, the tone it must use..."]}, {"cell_type": "code", "execution_count": null, "id": "f8bb7517-faa2-43ed-a95d-835de975f916", "metadata": {"tags": []}, "outputs": [], "source": ["template = ChatPromptTemplate.from_messages([\n", "    SystemMessagePromptTemplate.from_template(\n", "        \"\"\"You are a helpful, respectful, and honest assistant.\n", "        Answer each question clearly and concisely in a single response only.\n", "        Do not continue the conversation or simulate dialogue unless explicitly asked.\n", "        Never include any harmful, unethical, racist, sexist, toxic, dangerous, or illegal content.\n", "        Ensure that your responses are socially unbiased and positive in nature.\n", "        If a question does not make sense or is not factually coherent, explain why instead of trying to answer.\n", "        If you don't know the answer to a question, say \"I don't know\".\n", "        \"\"\"),\n", "    HumanMessagePromptTemplate.from_template(\"{input}\"),\n", "])"]}, {"cell_type": "markdown", "id": "849fbd67-220c-4a02-8e4e-7e0d1aa91588", "metadata": {}, "source": ["We are now ready to query the model!"]}, {"cell_type": "code", "execution_count": null, "id": "ca714bca-7cec-4afc-b275-fa389c05a993", "metadata": {"tags": []}, "outputs": [], "source": ["query = \"What is Artificial Intelligence?\"\n", "prompt = template.invoke({\"input\": query})\n", "response = llm.invoke(input=prompt)"]}, {"cell_type": "markdown", "id": "b4c37573-7917-4b32-bf21-9a280b31f3d4", "metadata": {}, "source": ["Some information, like the tokens that were consumed and generated are present in the metadata. That can be useful to measure the consumption of the model."]}, {"cell_type": "code", "execution_count": null, "id": "fe2fb653-ef68-4817-8360-13a6692e948e", "metadata": {}, "outputs": [], "source": ["print(json.dumps(response.usage_metadata, indent=2))"]}, {"cell_type": "markdown", "id": "c0089476-bba0-4093-8be8-1469780afaba", "metadata": {}, "source": ["You can come back to this notebook at section 3.7 for some optional exercises if you want."]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}