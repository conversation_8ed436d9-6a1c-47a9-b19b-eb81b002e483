{"cells": [{"cell_type": "markdown", "id": "991808d0-7d79-47f2-b8cc-764fe86a6156", "metadata": {"tags": []}, "source": ["# Your First Jupyter Notebook\n", "\n", "NOTE: **If you are already familiar with Jupyter Notebook**, and you understand how to edit and run cells, and how to use other Jupyter features, you can skip this notebook and continue to the next one.\n", "\n", "## What are <PERSON><PERSON><PERSON> notebooks?\n", "\n", "This is a [<PERSON><PERSON><PERSON>](https://jupyter.org/) notebook. You need to know only a few things to get started.\n", "\n", "* Jupyter notebooks are made up of _cells_ that can contain prose, executable code, or interactive UI elements.  This cell is a prose cell.\n", "\n", "* You can edit and execute cells. To edit a cell, double-click until the frame around it is highlighted. To execute a cell (whether you've edited it or not), click the play button at the top of the file window or select the cell and then press SHIFT+ENTER.\n", "\n", "     <figure>\n", "        <img src=\"../../content/modules/ROOT/assets/images/02/02-04-play-button.png\"  alt='missing' width=\"300\"  >\n", "     <figure/>\n", "        \n", "    <PERSON><PERSON><PERSON> executes the code in the cell, records the code output, and advances to the next cell.  \n", "\n", "* While a cell is executing, an asterisk shows in the margin, for example: `In: [*]`.  After a cell completes executing, the asterisk is replaced with a sequence number.\n", "\n", "\n", "Let's try it out now!  Try executing the next cell"]}, {"cell_type": "code", "execution_count": null, "id": "3b4dc1f1-8d6e-4869-ade8-cafc22baa817", "metadata": {"tags": []}, "outputs": [], "source": ["def print_some_text(entered_text):\n", "    print(\"This is what you entered: \\n\" + entered_text)\n", "\n", "\n", "my_text = \"This cell is for code. You can execute code by clicking on the play button above or hitting SHIFT+ENTER.\"\n", "\n", "print_some_text(my_text)"]}, {"cell_type": "code", "execution_count": null, "id": "70c9ed5e-60e1-42dd-b9c3-87fcfc79493e", "metadata": {"tags": []}, "outputs": [], "source": ["new_text = \"Next, let's see how you would write narrative text or documentation.\"\n", "\n", "# previously defined functions from another cell are still accessible.\n", "print_some_text(new_text)"]}, {"cell_type": "markdown", "id": "92bc6513-dbe5-4128-9399-d4d3ab9be845", "metadata": {}, "source": ["## This is a markdown cell\n", "### Chapter 1\n", "You can use **markdown formatting** to enter your text.\n", "\n", "*Double-click* on a cell to modify it.\n", "\n", "[Markdown reference](https://www.markdownguide.org/basic-syntax/)"]}, {"cell_type": "markdown", "id": "edaf739c-ff87-457b-914e-5d74428fa8ed", "metadata": {}, "source": ["## More Information\n", "\n", "For more information about <PERSON><PERSON><PERSON>, click the **Help menu** in the Jupyter window's menu bar.\n", "\n", "<figure>\n", "    <img src=\"../../content/modules/ROOT/assets/images/02/02-04-help-menu.png\"  alt='missing' width=\"300\"  >\n", "<figure/>"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 5}