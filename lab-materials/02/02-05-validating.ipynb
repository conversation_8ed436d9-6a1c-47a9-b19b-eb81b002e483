{"cells": [{"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Define functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import socket\n", "\n", "\n", "def check_service_connection(host, port, name):\n", "    try:\n", "        # Create a socket object\n", "        s = socket.create_connection((host, port), timeout=5)\n", "        s.close()\n", "        return True\n", "    except socket.timeout:\n", "        return False\n", "    except ConnectionRefusedError:\n", "        return False\n", "    except socket.gaierror as e:\n", "        return f\"Error: {name} - {e}\"\n", "\n", "\n", "def check_services(services):\n", "    for service in services:\n", "        host, port, name = service\n", "        result = check_service_connection(host, port, name)\n", "        if result is True:\n", "            print(f\"Success: {name} is reachable on {host}:{port}\")\n", "        elif result is False:\n", "            print(f\"Failure: {name} is NOT reachable on {host}:{port}\")\n", "        else:\n", "            print(result)"]}, {"cell_type": "markdown", "metadata": {"tags": []}, "source": ["# Execute checks"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# Confirm that services are reachable\n", "if __name__ == \"__main__\":\n", "    # List of services to check\n", "    services_to_check = [\n", "        (\"minio.ic-shared-minio.svc.cluster.local\", 9000, \"Minio\"),\n", "        (\"claimdb.ic-shared-db.svc.cluster.local\", 5432, \"Postgres Database\"),\n", "        (\"granite-3-1-8b-instruct-predictor.ic-shared-llm.svc.cluster.local\", 8080, \"LLM Service\"),\n", "        (\"qwen-predictor.ic-shared-llm.svc.cluster.local\", 8080, \"LLM Service-Qwen2.5\"),\n", "        (\"modelmesh-serving.ic-shared-img-det.svc.cluster.local\", 8033, \"ModelMesh\"),\n", "        (\"vectordb-milvus.ic-shared-milvus.svc.cluster.local\", 19530, \"Milvus Vector DB\"),\n", "        # Add more services as needed\n", "    ]\n", "\n", "    check_services(services_to_check)"]}], "metadata": {"kernelspec": {"display_name": "Python 3.11", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}